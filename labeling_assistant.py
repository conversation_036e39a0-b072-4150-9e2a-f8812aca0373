#!/usr/bin/env python3
"""
标注助手工具
提供标注建议和质量检查
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class LabelingAssistant:
    def __init__(self):
        self.labels_file = "data/captcha_labeled/labels.json"
        self.collection_dir = "data/captcha_collection"
        
    def get_labeling_suggestions(self):
        """获取标注建议"""
        # 获取当前进度
        progress = self.get_current_progress()
        
        suggestions = []
        
        # 数据量建议
        labeled_count = progress['labeled_count']
        remaining_count = progress['remaining_count']
        
        if labeled_count < 50:
            suggestions.append({
                "type": "quantity",
                "priority": "high",
                "message": f"建议继续标注到50个以上 (当前: {labeled_count})",
                "action": f"还需标注 {50 - labeled_count} 个"
            })
        elif labeled_count < 80:
            suggestions.append({
                "type": "quantity", 
                "priority": "medium",
                "message": f"建议标注到80个获得更好效果 (当前: {labeled_count})",
                "action": f"还需标注 {80 - labeled_count} 个"
            })
        
        # 数字分布建议
        digit_stats = progress['digit_stats']
        min_count = min(digit_stats.values())
        max_count = max(digit_stats.values())
        
        if max_count - min_count > 10:
            rare_digits = [d for d, c in digit_stats.items() if c == min_count]
            suggestions.append({
                "type": "balance",
                "priority": "medium", 
                "message": f"数字分布不均衡，某些数字较少",
                "action": f"注意标注包含数字 {', '.join(rare_digits)} 的验证码"
            })
        
        # 质量建议
        if min_count < 5:
            suggestions.append({
                "type": "quality",
                "priority": "high",
                "message": "某些数字出现次数过少，影响训练效果",
                "action": "确保每个数字至少出现5次"
            })
        
        return suggestions
    
    def get_current_progress(self):
        """获取当前进度"""
        # 获取总验证码数量
        total_count = 0
        if os.path.exists(self.collection_dir):
            total_count = len([f for f in os.listdir(self.collection_dir) if f.endswith('.png')])
        
        # 获取已标注数量
        labeled_count = 0
        digit_stats = {str(i): 0 for i in range(10)}
        
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    labels_data = json.load(f)
                
                labeled_count = len(labels_data.get("labels", {}))
                
                # 统计数字频率
                for label_info in labels_data.get("labels", {}).values():
                    label = label_info.get("label", "")
                    for digit in label:
                        if digit in digit_stats:
                            digit_stats[digit] += 1
                            
            except Exception as e:
                print(f"读取标注文件失败: {e}")
        
        return {
            "total_count": total_count,
            "labeled_count": labeled_count,
            "remaining_count": total_count - labeled_count,
            "progress_percent": (labeled_count / total_count * 100) if total_count > 0 else 0,
            "digit_stats": digit_stats
        }
    
    def print_labeling_guide(self):
        """打印标注指南"""
        print("🎯 验证码标注指南")
        print("=" * 40)
        
        print("\n📝 标注技巧:")
        print("1. 仔细观察放大的验证码图片")
        print("2. 区分相似字符:")
        print("   • 0 vs O: 数字0通常更圆润")
        print("   • 1 vs I: 数字1通常有底座")
        print("   • 5 vs S: 数字5有明显横线")
        print("   • 6 vs G: 数字6是封闭圆形")
        print("   • 8 vs B: 数字8上下对称")
        
        print("\n⌨️ 快捷键:")
        print("   • Enter: 提交标注")
        print("   • Esc: 跳过当前验证码")
        print("   • ←→: 切换验证码")
        
        print("\n✅ 标注原则:")
        print("   • 准确性优先，不确定就跳过")
        print("   • 保持标注标准一致")
        print("   • 每30分钟休息一次")
        
        print("\n🎯 当前目标:")
        progress = self.get_current_progress()
        suggestions = self.get_labeling_suggestions()
        
        for suggestion in suggestions:
            priority_icon = "🔴" if suggestion["priority"] == "high" else "🟡" if suggestion["priority"] == "medium" else "🟢"
            print(f"   {priority_icon} {suggestion['message']}")
            print(f"      → {suggestion['action']}")
    
    def estimate_time_remaining(self):
        """估算剩余时间"""
        progress = self.get_current_progress()
        remaining = progress['remaining_count']
        
        # 假设每个验证码标注需要10-15秒
        min_time = remaining * 10  # 秒
        max_time = remaining * 15  # 秒
        
        min_minutes = min_time // 60
        max_minutes = max_time // 60
        
        print(f"\n⏰ 时间估算:")
        print(f"   剩余验证码: {remaining} 个")
        print(f"   预计时间: {min_minutes}-{max_minutes} 分钟")
        print(f"   建议分批完成: 每次20-30个")
    
    def check_labeling_quality(self):
        """检查标注质量"""
        if not os.path.exists(self.labels_file):
            print("❌ 标注文件不存在")
            return
        
        try:
            with open(self.labels_file, 'r', encoding='utf-8') as f:
                labels_data = json.load(f)
            
            labels = labels_data.get("labels", {})
            
            print("🔍 标注质量检查")
            print("=" * 30)
            
            # 检查标注格式
            valid_count = 0
            invalid_count = 0
            
            for filename, label_info in labels.items():
                label = label_info.get("label", "")
                if len(label) == 4 and label.isdigit():
                    valid_count += 1
                else:
                    invalid_count += 1
                    print(f"⚠️ 无效标注: {filename} -> {label}")
            
            print(f"✅ 有效标注: {valid_count} 个")
            if invalid_count > 0:
                print(f"❌ 无效标注: {invalid_count} 个")
            
            # 检查数字分布
            digit_stats = {str(i): 0 for i in range(10)}
            for label_info in labels.values():
                label = label_info.get("label", "")
                for digit in label:
                    if digit in digit_stats:
                        digit_stats[digit] += 1
            
            print(f"\n🔢 数字分布检查:")
            min_count = min(digit_stats.values())
            max_count = max(digit_stats.values())
            
            for digit in "0123456789":
                count = digit_stats[digit]
                status = "✅" if count >= 5 else "⚠️" if count >= 2 else "❌"
                print(f"   {status} {digit}: {count} 次")
            
            print(f"\n📊 分布统计:")
            print(f"   最少: {min_count} 次")
            print(f"   最多: {max_count} 次")
            print(f"   差异: {max_count - min_count} 次")
            
            if max_count - min_count <= 5:
                print("✅ 数字分布均匀")
            elif max_count - min_count <= 10:
                print("📈 数字分布尚可")
            else:
                print("⚠️ 数字分布不均，建议平衡")
                
        except Exception as e:
            print(f"❌ 质量检查失败: {e}")
    
    def generate_progress_report(self):
        """生成进度报告"""
        progress = self.get_current_progress()
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "progress": progress,
            "suggestions": self.get_labeling_suggestions(),
            "quality_check": self.check_labeling_quality()
        }
        
        # 保存报告
        report_file = f"data/reports/labeling_progress_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("data/reports", exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"📄 进度报告已保存: {report_file}")
            
        except Exception as e:
            print(f"❌ 报告保存失败: {e}")

def main():
    """主函数"""
    assistant = LabelingAssistant()
    
    print("🤖 验证码标注助手")
    print("=" * 30)
    
    choice = input("""
选择功能:
1. 查看标注指南
2. 检查标注质量
3. 估算剩余时间
4. 生成进度报告
5. 完整助手 (推荐)
请输入选择 (1-5): """).strip()
    
    if choice == "1":
        assistant.print_labeling_guide()
    
    elif choice == "2":
        assistant.check_labeling_quality()
    
    elif choice == "3":
        assistant.estimate_time_remaining()
    
    elif choice == "4":
        assistant.generate_progress_report()
    
    elif choice == "5":
        print("🚀 完整标注助手")
        print("=" * 30)
        assistant.print_labeling_guide()
        assistant.estimate_time_remaining()
        assistant.check_labeling_quality()
    
    else:
        print("无效选择，显示标注指南")
        assistant.print_labeling_guide()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
