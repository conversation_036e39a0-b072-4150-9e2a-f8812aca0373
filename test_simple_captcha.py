#!/usr/bin/env python3
"""
简化版验证码识别测试
"""

import sys
import os
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_simple_captcha():
    """测试简化版验证码识别"""
    print("=== 简化版验证码识别测试 ===")
    
    try:
        from backend.app.services.captcha_service_simple import simple_captcha_service
        
        # 检查是否有现有的验证码图片
        captcha_dir = "data/captcha"
        if os.path.exists(captcha_dir):
            captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
            if captcha_files:
                # 使用最新的验证码文件
                latest_captcha = max(captcha_files, key=lambda x: os.path.getctime(os.path.join(captcha_dir, x)))
                captcha_path = os.path.join(captcha_dir, latest_captcha)
                
                print(f"使用现有验证码文件: {captcha_path}")
                
                # 测试识别
                result = simple_captcha_service.recognize_captcha(captcha_path)
                print(f"识别结果: {result}")
                
                if result["success"]:
                    print(f"✅ 识别成功: {result['best_result']} (置信度: {result['confidence']})")
                    print(f"所有结果: {result['all_results']}")
                    
                    # 询问是否正确
                    user_input = input(f"识别结果是否正确？实际验证码是什么？(直接回车表示正确): ").strip()
                    if user_input:
                        print(f"实际验证码: {user_input}")
                        print(f"识别结果: {result['best_result']}")
                        print(f"识别准确性: {'✅ 正确' if user_input.upper() == result['best_result'].upper() else '❌ 错误'}")
                    else:
                        print("✅ 用户确认识别正确")
                        
                else:
                    print(f"❌ 识别失败: {result['message']}")
                    
            else:
                print("❌ 没有找到验证码图片文件")
                print("请先运行获取验证码的测试")
        else:
            print("❌ 验证码目录不存在")
            print("请先运行获取验证码的测试")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_with_new_captcha():
    """获取新验证码并测试识别"""
    print("=== 获取新验证码并测试识别 ===")
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.app.services.captcha_service_simple import simple_captcha_service
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务...")
        eir_service = EIRService()
        
        print("2. 获取验证码...")
        result = await eir_service.get_login_captcha()
        print(f"验证码获取结果: {result}")
        
        if result["success"]:
            captcha_path = result["captcha_path"]
            print(f"✅ 验证码保存在: {captcha_path}")
            
            print("3. 使用简化版服务识别验证码...")
            recognition_result = simple_captcha_service.recognize_captcha(captcha_path)
            print(f"识别结果: {recognition_result}")
            
            if recognition_result["success"]:
                best_result = recognition_result["best_result"]
                confidence = recognition_result["confidence"]
                all_results = recognition_result["all_results"]
                
                print(f"✅ 最佳识别结果: {best_result}")
                print(f"✅ 置信度: {confidence}")
                print(f"✅ 所有识别结果: {all_results}")
                
                # 询问用户是否使用识别结果进行登录
                user_choice = input(f"\n是否使用识别结果 '{best_result}' 进行登录测试？(y/n): ").strip().lower()
                
                if user_choice == 'y':
                    print(f"4. 使用识别结果 '{best_result}' 进行登录...")
                    login_result = await eir_service.login(best_result)
                    print(f"登录结果: {login_result}")
                    
                    if login_result["success"]:
                        print("🎉 自动登录成功！")
                    else:
                        print(f"❌ 自动登录失败: {login_result['message']}")
                        
                        # 询问正确的验证码
                        correct_code = input("请输入正确的验证码: ").strip()
                        if correct_code:
                            print(f"使用正确验证码 '{correct_code}' 重试...")
                            retry_result = await eir_service.login(correct_code)
                            print(f"重试登录结果: {retry_result}")
                            
                            # 分析识别准确性
                            print(f"\n识别准确性分析:")
                            print(f"识别结果: {best_result}")
                            print(f"正确答案: {correct_code}")
                            print(f"准确性: {'✅ 正确' if correct_code.upper() == best_result.upper() else '❌ 错误'}")
                else:
                    print("跳过登录测试")
                    
            else:
                print(f"❌ 验证码识别失败: {recognition_result['message']}")
        else:
            print(f"❌ 验证码获取失败: {result['message']}")
        
        # 清理资源
        eir_service.close()
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始简化版验证码识别测试...")
    
    choice = input("选择测试模式:\n1. 使用现有验证码图片测试\n2. 获取新验证码并测试\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        asyncio.run(test_simple_captcha())
    elif choice == "2":
        asyncio.run(test_with_new_captcha())
    else:
        print("无效选择，默认使用现有验证码测试")
        asyncio.run(test_simple_captcha())

if __name__ == "__main__":
    main()
