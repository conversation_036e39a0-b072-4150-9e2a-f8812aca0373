#!/usr/bin/env python3
"""
检查EIR验证码URL
"""

import requests
import time

def check_eir_urls():
    """检查EIR验证码URL"""
    print("🔍 检查EIR验证码URL")
    
    base_url = "https://eir.cmclink.com"
    
    # 可能的验证码URL
    possible_urls = [
        f"{base_url}/ValidateCode.aspx",
        f"{base_url}/validatecode.aspx",
        f"{base_url}/captcha.aspx",
        f"{base_url}/Captcha.aspx",
        f"{base_url}/VerifyCode.aspx",
        f"{base_url}/verifycode.aspx",
        f"{base_url}/Code.aspx",
        f"{base_url}/code.aspx",
        f"{base_url}/CheckCode.aspx",
        f"{base_url}/checkcode.aspx"
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    print("1. 首先访问登录页面...")
    try:
        response = session.get(base_url, timeout=10)
        print(f"登录页面状态: {response.status_code}")
        
        if response.status_code == 200:
            # 查找验证码相关的内容
            content = response.text.lower()
            
            print("\n2. 在页面中查找验证码相关信息...")
            
            # 查找可能的验证码图片标签
            if 'validatecode' in content:
                print("✅ 找到 'validatecode' 关键词")
            if 'captcha' in content:
                print("✅ 找到 'captcha' 关键词")
            if 'verifycode' in content:
                print("✅ 找到 'verifycode' 关键词")
            if 'checkcode' in content:
                print("✅ 找到 'checkcode' 关键词")
            
            # 查找img标签
            import re
            img_tags = re.findall(r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>', content, re.IGNORECASE)
            
            print(f"\n3. 找到的图片标签 ({len(img_tags)} 个):")
            for img_src in img_tags:
                if any(keyword in img_src.lower() for keyword in ['validate', 'captcha', 'verify', 'check', 'code']):
                    print(f"  🎯 可能的验证码: {img_src}")
                else:
                    print(f"  📷 其他图片: {img_src}")
        
    except Exception as e:
        print(f"❌ 访问登录页面失败: {e}")
        return
    
    print("\n4. 测试可能的验证码URL...")
    for url in possible_urls:
        try:
            timestamp = int(time.time() * 1000)
            test_url = f"{url}?t={timestamp}"
            
            response = session.get(test_url, timeout=5)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                content_length = len(response.content)
                
                if 'image' in content_type and content_length > 500:
                    print(f"✅ 找到验证码URL: {test_url}")
                    print(f"   Content-Type: {content_type}")
                    print(f"   Content-Length: {content_length}")
                    
                    # 保存测试图片
                    test_filename = f"test_captcha_{timestamp}.png"
                    with open(test_filename, 'wb') as f:
                        f.write(response.content)
                    print(f"   已保存测试图片: {test_filename}")
                    
                else:
                    print(f"⚠️  {url}: 状态200但不是图片 (类型: {content_type}, 大小: {content_length})")
            else:
                print(f"❌ {url}: 状态码 {response.status_code}")
                
        except Exception as e:
            print(f"❌ {url}: 异常 {e}")
        
        time.sleep(0.5)  # 避免请求过快

if __name__ == "__main__":
    check_eir_urls()
