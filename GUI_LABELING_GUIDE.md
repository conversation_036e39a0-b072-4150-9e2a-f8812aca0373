# 🏷️ GUI验证码标注工具使用指南

## 🎯 工具概述

这是一个基于Tkinter的图形界面验证码标注工具，提供直观的图片显示和便捷的标注操作。

## 🚀 启动工具

```bash
# 启动GUI标注工具
python gui_labeling_tool.py
```

## 📱 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────┐
│ 🏷️ 验证码标注工具    已标注:X | 待标注:Y | 进度:Z% │
├─────────────────────────────────────────────┤
│                                             │
│            验证码图片显示区域                  │
│         (放大8倍，清晰可见)                   │
│                                             │
│              文件名显示                      │
├─────────────────────────────────────────────┤
│ 验证码: [____] (输入4位数字)                  │
├─────────────────────────────────────────────┤
│ ✅提交 ⏭️跳过 ⬅️上一张 ➡️下一张    💾保存 📊统计 │
├─────────────────────────────────────────────┤
│ 进度:1/35 (2.9%)     快捷键提示              │
└─────────────────────────────────────────────┘
```

### 功能区域说明

#### 1. **图片显示区域**
- 显示当前验证码图片（放大8倍）
- 清晰显示验证码内容
- 显示当前文件名

#### 2. **输入区域**
- 验证码输入框（自动限制4位数字）
- 实时输入验证
- 输入完成自动启用提交按钮

#### 3. **操作按钮**
- **✅ 提交**: 保存当前标注
- **⏭️ 跳过**: 跳过当前验证码
- **⬅️ 上一张**: 查看上一张图片
- **➡️ 下一张**: 查看下一张图片
- **💾 保存**: 手动保存标注数据
- **📊 统计**: 查看详细统计信息

#### 4. **状态栏**
- 显示当前进度
- 快捷键提示

## ⌨️ 快捷键操作

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Enter` | 提交标注 | 输入4位数字后按回车提交 |
| `Esc` | 跳过当前 | 跳过当前验证码 |
| `←` | 上一张 | 查看上一张图片 |
| `→` | 下一张 | 查看下一张图片 |

## 📝 标注流程

### 1. 启动工具
```bash
python gui_labeling_tool.py
```

### 2. 查看当前状态
- 界面顶部显示总体统计
- 状态栏显示当前进度

### 3. 标注验证码
1. **观察图片**: 仔细查看放大的验证码图片
2. **输入数字**: 在输入框中输入4位数字
3. **提交标注**: 点击"提交"按钮或按Enter键
4. **自动跳转**: 系统自动跳转到下一张图片

### 4. 特殊操作
- **跳过难识别的**: 点击"跳过"或按Esc键
- **重新查看**: 使用"上一张"/"下一张"按钮浏览
- **查看统计**: 点击"统计"按钮查看详细信息

## 📊 统计功能

### 实时统计显示
- **已标注数量**: 已完成标注的验证码数
- **待标注数量**: 还需要标注的验证码数
- **完成进度**: 标注完成的百分比

### 详细统计信息
点击"📊 统计"按钮查看：
- 总验证码数量
- 标注完成情况
- 数字频率分布
- 各数字出现次数

## 💾 数据保存

### 自动保存
- 每次提交标注后自动保存
- 数据保存在 `data/captcha_labeled/labels.json`

### 手动保存
- 点击"💾 保存"按钮手动保存
- 确保数据不丢失

## 🎯 标注技巧

### 1. 图片识别技巧
- **仔细观察**: 验证码已放大8倍，细节清晰
- **区分相似字符**: 
  - `0` vs `O`: 数字0通常更圆
  - `1` vs `I` vs `l`: 数字1通常有底座
  - `5` vs `S`: 数字5有明显的横线
  - `6` vs `G`: 数字6是封闭的圆形
  - `8` vs `B`: 数字8上下对称

### 2. 标注原则
- **准确性优先**: 不确定的验证码选择跳过
- **一致性**: 保持标注标准一致
- **效率**: 使用快捷键提高标注速度

### 3. 质量控制
- 定期查看统计信息
- 检查数字分布是否合理
- 必要时重新标注有疑问的图片

## 🔧 故障排除

### 常见问题

#### 1. 图片显示问题
**问题**: 图片无法显示或显示异常
**解决**: 
- 检查图片文件是否存在
- 确认图片格式正确
- 重启工具

#### 2. 输入框问题
**问题**: 无法输入或输入限制异常
**解决**:
- 点击输入框确保焦点
- 只能输入数字，最多4位
- 清空输入框重新输入

#### 3. 保存失败
**问题**: 标注数据保存失败
**解决**:
- 检查目录权限
- 确保磁盘空间充足
- 手动点击保存按钮

#### 4. 程序卡死
**问题**: 界面无响应
**解决**:
- 关闭程序重新启动
- 检查系统资源使用情况

### 数据恢复
如果程序异常退出：
1. 重新启动工具
2. 已保存的标注会自动加载
3. 继续未完成的标注工作

## 📈 完成后的操作

### 1. 检查标注质量
```bash
# 查看标注统计
python test_captcha_labeling.py
# 选择"3. 查看统计信息"
```

### 2. 导出训练数据
```bash
# 导出训练数据
python test_captcha_labeling.py
# 选择"4. 导出训练数据"
```

### 3. 开始模型训练
```bash
# 训练验证码识别模型
python train_captcha_model.py train
```

## 🎉 标注目标

### 建议标注数量
- **最少**: 100个验证码（基础训练）
- **推荐**: 300-500个验证码（良好效果）
- **最佳**: 1000+个验证码（最佳效果）

### 数据质量要求
- **准确率**: >95%
- **数字分布**: 每个数字(0-9)至少出现20次
- **一致性**: 标注标准保持一致

## 💡 高效标注建议

### 1. 分批标注
- 每次标注50-100个
- 避免疲劳导致错误

### 2. 时间安排
- 选择精神状态好的时间
- 每30分钟休息一次

### 3. 环境设置
- 确保良好的显示器和光线
- 调整合适的窗口大小

### 4. 快捷键使用
- 熟练使用Enter、Esc、方向键
- 提高标注效率

完成标注后，您就可以开始训练高精度的验证码识别模型了！🚀
