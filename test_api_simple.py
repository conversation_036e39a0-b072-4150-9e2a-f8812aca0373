#!/usr/bin/env python3
"""
简化的API测试 - 不依赖ChromeDriver
"""

import requests
import json
import time

def test_api_endpoints():
    """测试API接口"""
    print("=== EIR API接口测试 (无ChromeDriver) ===")
    
    base_url = "http://localhost:8001"
    api_base = f"{base_url}/api"
    
    # 1. 测试根路径
    print("\n1. 测试根路径...")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ 状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 响应: {result}")
        else:
            print(f"❌ 响应失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接错误: {e}")
        return False
    
    # 2. 测试登录状态检查
    print("\n2. 测试登录状态检查...")
    try:
        response = requests.get(f"{api_base}/eir/status", timeout=5)
        print(f"✅ 状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 响应失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求错误: {e}")
    
    # 3. 测试获取验证码（预期会失败）
    print("\n3. 测试获取验证码（预期失败 - 无ChromeDriver）...")
    try:
        response = requests.get(f"{api_base}/eir/captcha", timeout=10)
        print(f"✅ 状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📝 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            if not result.get("success"):
                print("⚠️  预期的失败 - 需要安装ChromeDriver")
        else:
            print(f"❌ 响应失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求错误: {e}")
    
    # 4. 测试API文档
    print("\n4. 测试API文档...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        print(f"✅ 状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ API文档可访问")
            print(f"🌐 访问地址: {base_url}/docs")
        else:
            print(f"❌ API文档不可访问: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 文档访问错误: {e}")
    
    return True

def test_database():
    """测试数据库功能"""
    print("\n=== 数据库功能测试 ===")
    
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        
        from backend.app.models.database import DatabaseManager
        
        # 创建数据库实例
        db = DatabaseManager()
        
        # 测试日志记录
        print("1. 测试操作日志记录...")
        db.log_operation("test", "API", "API测试", "success")
        print("✅ 日志记录成功")
        
        # 测试订单保存
        print("2. 测试订单保存...")
        order_id = db.save_order("TEST001", "测试船公司", "20GP")
        if order_id > 0:
            print(f"✅ 订单保存成功，ID: {order_id}")
        else:
            print("❌ 订单保存失败")
        
        # 测试Cookie保存（模拟数据）
        print("3. 测试Cookie保存...")
        test_cookies = [{"name": "test", "value": "123", "domain": "test.com"}]
        success = db.save_cookies("TEST", "testuser", test_cookies)
        if success:
            print("✅ Cookie保存成功")
            
            # 测试Cookie获取
            cookies = db.get_cookies("TEST", "testuser")
            if cookies:
                print(f"✅ Cookie获取成功: {len(cookies)} 个cookie")
            else:
                print("❌ Cookie获取失败")
        else:
            print("❌ Cookie保存失败")
        
        print("✅ 数据库功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始EIR系统测试...")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试API接口
    api_success = test_api_endpoints()
    
    # 测试数据库
    db_success = test_database()
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"API接口测试: {'✅ 通过' if api_success else '❌ 失败'}")
    print(f"数据库测试: {'✅ 通过' if db_success else '❌ 失败'}")
    
    if api_success and db_success:
        print("\n🎉 基础功能测试全部通过！")
        print("📝 下一步: 安装ChromeDriver以启用完整的自动化功能")
        print("🔗 ChromeDriver下载: https://chromedriver.chromium.org/")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
