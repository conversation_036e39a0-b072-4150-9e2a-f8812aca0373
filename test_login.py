#!/usr/bin/env python3
"""
EIR登录功能测试脚本
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.services.eir_service import EIRService
from backend.config import Config

async def test_login():
    """测试EIR登录功能"""
    print("=== EIR登录功能测试 ===")
    
    # 创建必要的目录
    Config.create_directories()
    
    # 创建EIR服务实例
    eir_service = EIRService()
    
    try:
        # 1. 获取验证码
        print("\n1. 获取登录验证码...")
        captcha_result = await eir_service.get_login_captcha()
        print(f"验证码获取结果: {captcha_result}")
        
        if not captcha_result["success"]:
            print("验证码获取失败，退出测试")
            return
        
        # 显示验证码图片路径
        captcha_path = captcha_result["captcha_path"]
        print(f"验证码图片保存在: {captcha_path}")
        print("请查看验证码图片并输入验证码")
        
        # 2. 用户输入验证码
        captcha_code = input("请输入验证码: ").strip()
        
        if not captcha_code:
            print("验证码不能为空")
            return
        
        # 3. 执行登录
        print(f"\n2. 使用验证码 '{captcha_code}' 执行登录...")
        login_result = await eir_service.login(captcha_code)
        print(f"登录结果: {login_result}")
        
        # 4. 检查登录状态
        print("\n3. 检查登录状态...")
        status_result = await eir_service.check_login_status()
        print(f"登录状态: {status_result}")
        
        if login_result["success"]:
            print("\n✅ 登录成功！Cookies已保存，下次可以直接使用。")
        else:
            print(f"\n❌ 登录失败: {login_result['message']}")
            
            # 如果验证码错误，可以刷新验证码重试
            if "验证码" in login_result["message"]:
                retry = input("是否刷新验证码重试？(y/n): ").strip().lower()
                if retry == 'y':
                    print("\n刷新验证码...")
                    refresh_result = await eir_service.refresh_captcha()
                    print(f"刷新结果: {refresh_result}")
                    
                    if refresh_result["success"]:
                        new_captcha_path = refresh_result["captcha_path"]
                        print(f"新验证码图片: {new_captcha_path}")
                        new_captcha_code = input("请输入新验证码: ").strip()
                        
                        if new_captcha_code:
                            print(f"使用新验证码 '{new_captcha_code}' 重新登录...")
                            retry_login_result = await eir_service.login(new_captcha_code)
                            print(f"重试登录结果: {retry_login_result}")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
    finally:
        # 清理资源
        print("\n清理资源...")
        eir_service.close()
        print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_login())
