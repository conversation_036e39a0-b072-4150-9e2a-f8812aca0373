#!/usr/bin/env python3
"""
简化的验证码收集测试
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_single_captcha_collection():
    """测试单个验证码收集"""
    print("🧪 测试单个验证码收集")
    
    try:
        from backend.app.services.captcha_collector import captcha_collector
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 初始化验证码收集器...")
        
        # 初始化浏览器
        if not captcha_collector.init_driver():
            print("❌ 浏览器初始化失败")
            return False
        
        print("✅ 浏览器初始化成功")
        
        try:
            print("2. 尝试下载单个验证码...")
            
            # 下载单个验证码进行测试
            session_id = "test_session"
            captcha_info = captcha_collector.download_single_captcha(session_id, 1)
            
            if captcha_info:
                print("✅ 验证码下载成功！")
                print(f"文件名: {captcha_info.get('filename')}")
                print(f"文件路径: {captcha_info.get('filepath')}")
                print(f"文件大小: {captcha_info.get('file_size')} bytes")
                
                # 显示图片特征
                features = captcha_info.get('features', {})
                print(f"图片尺寸: {features.get('width')}x{features.get('height')}")
                print(f"平均亮度: {features.get('mean_intensity', 0):.1f}")
                
                return True
            else:
                print("❌ 验证码下载失败")
                return False
                
        finally:
            # 清理资源
            captcha_collector.close()
            print("🧹 资源清理完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_collection(count=5):
    """测试批量收集"""
    print(f"📦 测试批量收集 {count} 个验证码")
    
    try:
        from backend.app.services.captcha_collector import captcha_collector
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("开始批量收集...")
        result = captcha_collector.collect_captchas(count, 2)  # 小批次
        
        print(f"\n📊 收集结果:")
        print(f"状态: {result.get('status')}")
        print(f"成功: {result.get('success_count', 0)}")
        print(f"失败: {result.get('failed_count', 0)}")
        print(f"总计: {result.get('collected_count', 0)}")
        
        if result.get('status') == 'completed':
            print("✅ 批量收集完成！")
            
            # 显示统计信息
            stats = captcha_collector.get_collection_statistics()
            print(f"\n📈 统计信息:")
            print(f"累计收集: {stats.get('total_collected', 0)} 个")
            
            return True
        else:
            print(f"⚠️ 收集未完成: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 批量收集失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 验证码收集测试")
    print("=" * 50)
    
    choice = input("""
选择测试模式:
1. 测试单个验证码收集
2. 测试批量收集 (5个)
3. 测试批量收集 (10个)
请输入选择 (1/2/3): """).strip()
    
    if choice == "1":
        success = test_single_captcha_collection()
    elif choice == "2":
        success = test_batch_collection(5)
    elif choice == "3":
        success = test_batch_collection(10)
    else:
        print("无效选择，默认测试单个验证码收集")
        success = test_single_captcha_collection()
    
    if success:
        print("\n🎉 测试成功完成！")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
