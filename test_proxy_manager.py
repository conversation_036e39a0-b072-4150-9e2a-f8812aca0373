#!/usr/bin/env python3
"""
测试智能代理管理器
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_proxy_manager():
    """测试代理管理器"""
    print("🌐 测试智能代理管理器")
    
    try:
        from backend.app.services.proxy_manager import proxy_manager
        
        # 测试不同网站的代理配置
        test_urls = [
            "https://eir.cmclink.com",           # 国内网站，不需要代理
            "https://www.baidu.com",             # 国内网站，不需要代理
            "https://www.google.com",            # 被墙网站，需要代理
            "https://github.com",                # 被墙网站，需要代理
            "https://api.ocr.space",             # 被墙网站，需要代理
            "https://stackoverflow.com",         # 被墙网站，需要代理
        ]
        
        print("\n📊 网站代理配置测试:")
        print("-" * 80)
        print(f"{'网站':<30} {'是否被墙':<10} {'使用代理':<10} {'代理配置'}")
        print("-" * 80)
        
        for url in test_urls:
            info = proxy_manager.get_domain_info(url)
            domain = info.get('domain', 'N/A')
            is_blocked = "是" if info.get('is_blocked', False) else "否"
            should_proxy = "是" if info.get('should_use_proxy', False) else "否"
            proxy_config = "有代理" if info.get('proxy_config') else "无代理"
            
            print(f"{domain:<30} {is_blocked:<10} {should_proxy:<10} {proxy_config}")
        
        print("\n🔧 Chrome代理参数测试:")
        for url in test_urls:
            domain = proxy_manager.get_domain_info(url).get('domain', 'N/A')
            proxy_args = proxy_manager.get_chrome_proxy_args(url)
            if proxy_args:
                print(f"  {domain}: {proxy_args}")
            else:
                print(f"  {domain}: 无代理参数")
        
        print("\n✅ 代理管理器测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_proxy_manager()
