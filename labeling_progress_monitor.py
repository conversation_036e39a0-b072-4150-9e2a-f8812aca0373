#!/usr/bin/env python3
"""
标注进度监控工具
实时监控标注进度和数据质量
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class LabelingProgressMonitor:
    def __init__(self):
        self.labels_file = "data/captcha_labeled/labels.json"
        self.collection_dir = "data/captcha_collection"
        
    def get_current_progress(self):
        """获取当前标注进度"""
        # 获取总验证码数量
        total_count = 0
        if os.path.exists(self.collection_dir):
            total_count = len([f for f in os.listdir(self.collection_dir) if f.endswith('.png')])
        
        # 获取已标注数量
        labeled_count = 0
        digit_stats = {str(i): 0 for i in range(10)}
        
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    labels_data = json.load(f)
                
                labeled_count = len(labels_data.get("labels", {}))
                
                # 统计数字频率
                for label_info in labels_data.get("labels", {}).values():
                    label = label_info.get("label", "")
                    for digit in label:
                        if digit in digit_stats:
                            digit_stats[digit] += 1
                            
            except Exception as e:
                print(f"读取标注文件失败: {e}")
        
        return {
            "total_count": total_count,
            "labeled_count": labeled_count,
            "remaining_count": total_count - labeled_count,
            "progress_percent": (labeled_count / total_count * 100) if total_count > 0 else 0,
            "digit_stats": digit_stats
        }
    
    def print_progress_report(self):
        """打印进度报告"""
        progress = self.get_current_progress()
        
        print("📊 验证码标注进度报告")
        print("=" * 50)
        print(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📦 总验证码数: {progress['total_count']}")
        print(f"✅ 已标注数: {progress['labeled_count']}")
        print(f"⏳ 待标注数: {progress['remaining_count']}")
        print(f"📈 完成进度: {progress['progress_percent']:.1f}%")
        
        # 进度条
        bar_length = 30
        filled_length = int(bar_length * progress['progress_percent'] / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        print(f"📊 进度条: [{bar}] {progress['progress_percent']:.1f}%")
        
        # 数字分布统计
        print(f"\n🔢 数字频率分布:")
        digit_stats = progress['digit_stats']
        total_digits = sum(digit_stats.values())
        
        for digit in "0123456789":
            count = digit_stats[digit]
            percentage = (count / total_digits * 100) if total_digits > 0 else 0
            bar_len = int(count / max(digit_stats.values()) * 20) if max(digit_stats.values()) > 0 else 0
            digit_bar = "▓" * bar_len + "░" * (20 - bar_len)
            print(f"  {digit}: {count:3d} 次 ({percentage:5.1f}%) [{digit_bar}]")
        
        # 数据质量评估
        print(f"\n📋 数据质量评估:")
        min_count = min(digit_stats.values())
        max_count = max(digit_stats.values())
        avg_count = total_digits / 10 if total_digits > 0 else 0
        
        print(f"  最少出现: {min_count} 次")
        print(f"  最多出现: {max_count} 次") 
        print(f"  平均出现: {avg_count:.1f} 次")
        
        if min_count < 5:
            print(f"  ⚠️  某些数字出现次数较少，建议继续标注")
        elif min_count >= 10:
            print(f"  ✅ 数字分布良好，适合训练")
        else:
            print(f"  📈 数字分布尚可，可以开始训练")
        
        # 训练建议
        print(f"\n💡 训练建议:")
        if progress['labeled_count'] < 50:
            print(f"  📝 建议继续标注到50个以上再开始训练")
        elif progress['labeled_count'] < 100:
            print(f"  🤖 可以开始训练，但建议标注到100个获得更好效果")
        else:
            print(f"  🚀 数据量充足，可以开始高质量训练")
        
        return progress
    
    def monitor_realtime(self, interval=10):
        """实时监控标注进度"""
        print("🔄 开始实时监控标注进度...")
        print("按 Ctrl+C 停止监控")
        
        last_count = 0
        
        try:
            while True:
                os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
                
                progress = self.print_progress_report()
                current_count = progress['labeled_count']
                
                if current_count > last_count:
                    print(f"\n🎉 新增标注: {current_count - last_count} 个")
                    last_count = current_count
                
                print(f"\n⏰ 下次更新: {interval} 秒后...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print(f"\n\n✅ 监控已停止")
    
    def check_training_readiness(self):
        """检查训练准备情况"""
        progress = self.get_current_progress()
        
        print("🔍 训练准备情况检查")
        print("=" * 30)
        
        # 检查数据量
        labeled_count = progress['labeled_count']
        if labeled_count < 30:
            print(f"❌ 数据量不足: {labeled_count}/30 (最低要求)")
            return False
        elif labeled_count < 50:
            print(f"⚠️  数据量较少: {labeled_count}/50 (建议)")
        elif labeled_count < 100:
            print(f"✅ 数据量良好: {labeled_count}/100 (推荐)")
        else:
            print(f"🚀 数据量充足: {labeled_count}/100+ (优秀)")
        
        # 检查数字分布
        digit_stats = progress['digit_stats']
        min_count = min(digit_stats.values())
        
        if min_count == 0:
            missing_digits = [d for d, c in digit_stats.items() if c == 0]
            print(f"❌ 缺少数字: {', '.join(missing_digits)}")
            return False
        elif min_count < 3:
            rare_digits = [d for d, c in digit_stats.items() if c < 3]
            print(f"⚠️  稀少数字: {', '.join(rare_digits)} (少于3次)")
        else:
            print(f"✅ 数字分布均匀: 最少 {min_count} 次")
        
        # 总体评估
        if labeled_count >= 50 and min_count >= 3:
            print(f"\n🎯 结论: 可以开始训练")
            return True
        elif labeled_count >= 30 and min_count >= 1:
            print(f"\n📝 结论: 可以尝试训练，但建议继续标注")
            return True
        else:
            print(f"\n⏳ 结论: 需要更多标注数据")
            return False

def main():
    """主函数"""
    monitor = LabelingProgressMonitor()
    
    print("📊 验证码标注进度监控工具")
    print("=" * 40)
    
    choice = input("""
选择功能:
1. 查看当前进度
2. 实时监控进度 (每10秒更新)
3. 检查训练准备情况
4. 快速进度检查 (每5秒更新)
请输入选择 (1-4): """).strip()
    
    if choice == "1":
        monitor.print_progress_report()
    
    elif choice == "2":
        monitor.monitor_realtime(10)
    
    elif choice == "3":
        monitor.check_training_readiness()
    
    elif choice == "4":
        monitor.monitor_realtime(5)
    
    else:
        print("无效选择，显示当前进度")
        monitor.print_progress_report()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
