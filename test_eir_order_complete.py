#!/usr/bin/env python3
"""
完整的EIR打单功能测试
基于发现的准确页面元素进行自动化测试
"""

import sys
import os
import asyncio
import time

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_complete_order_process():
    """测试完整的打单流程"""
    print("🎯 EIR完整打单流程测试")
    print("=" * 60)
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import Select
        
        # 创建必要的目录
        Config.create_directories()
        
        print("🔧 初始化EIR服务...")
        eir_service = EIRService()
        
        print("🔐 执行自动登录...")
        login_result = await eir_service.auto_login()
        
        if not login_result.get("success"):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        print("✅ 登录成功！开始打单流程...")
        
        # 获取浏览器实例
        automation = eir_service.automation
        driver = automation.driver
        
        if not driver:
            print("❌ 无法获取浏览器实例")
            return False
        
        # 等待页面完全加载
        time.sleep(3)
        
        # 步骤1: 导航到打单页面
        print("\n🧭 步骤1: 导航到打单页面...")
        
        try:
            # 点击业务办理菜单
            print("   1.1 点击【业务办理】...")
            business_menu = driver.find_element(By.ID, "UCHead1_rpFirstMeun_ctl00_liClass")
            business_link = business_menu.find_element(By.TAG_NAME, "a")
            business_link.click()
            time.sleep(2)
            print("   ✅ 业务办理菜单点击成功")
            
            # 点击出口EIR缴费并打单
            print("   1.2 点击【出口EIR缴费并打单】...")
            export_eir_menu = driver.find_element(By.ID, "UCHead1_rpSecondMenu_ctl00_liSubClass")
            export_eir_link = export_eir_menu.find_element(By.TAG_NAME, "a")
            export_eir_link.click()
            time.sleep(3)
            print("   ✅ 出口EIR缴费并打单菜单点击成功")
            
        except Exception as e:
            print(f"   ❌ 导航到打单页面失败: {e}")
            return False
        
        # 步骤2: 分析打单页面
        print("\n🔍 步骤2: 分析打单页面...")
        
        current_url = driver.current_url
        page_title = driver.title
        print(f"   当前URL: {current_url}")
        print(f"   页面标题: {page_title}")
        
        # 检查关键元素是否存在
        try:
            # 检查船公司选择框
            carrier_select = driver.find_element(By.ID, "ddlCarrier_DropDownList1")
            select = Select(carrier_select)
            options = [option.text for option in select.options]
            print(f"   ✅ 船公司选择框: 找到 {len(options)} 个选项")
            print(f"   📋 可选船公司: {options[:3]}...")
            
            # 检查订舱号输入框
            so_input = driver.find_element(By.ID, "txtBookingNo")
            print("   ✅ 订舱号输入框: 已找到")
            
            # 检查查询按钮
            query_button = driver.find_element(By.ID, "imgQuery")
            print("   ✅ 查询按钮: 已找到")
            
        except Exception as e:
            print(f"   ❌ 关键元素检查失败: {e}")
            return False
        
        # 步骤3: 获取测试数据
        print("\n📝 步骤3: 获取测试数据...")
        
        # 获取用户输入的测试数据
        print("请提供测试数据:")
        
        # 显示可选的船公司
        print(f"\n可选船公司 (前10个):")
        for i, option in enumerate(options[:10], 1):
            print(f"   {i}. {option}")
        
        carrier_choice = input("\n请输入船公司名称或编号: ").strip()
        
        # 如果输入的是数字，转换为船公司名称
        if carrier_choice.isdigit():
            choice_index = int(carrier_choice) - 1
            if 0 <= choice_index < len(options):
                selected_carrier = options[choice_index]
            else:
                print("❌ 无效的船公司编号")
                return False
        else:
            selected_carrier = carrier_choice
        
        so_number = input("请输入订舱号(S/O): ").strip()
        
        if not selected_carrier or not so_number:
            print("❌ 船公司和订舱号不能为空")
            return False
        
        print(f"   测试数据: 船公司={selected_carrier}, 订舱号={so_number}")
        
        # 步骤4: 执行打单操作
        print("\n🚀 步骤4: 执行打单操作...")
        
        try:
            # 4.1 选择船公司
            print("   4.1 选择船公司...")
            carrier_select = driver.find_element(By.ID, "ddlCarrier_DropDownList1")
            select = Select(carrier_select)
            
            # 尝试精确匹配
            try:
                select.select_by_visible_text(selected_carrier)
                print(f"   ✅ 选择船公司: {selected_carrier}")
            except:
                # 尝试模糊匹配
                found = False
                for option in select.options:
                    if selected_carrier.lower() in option.text.lower():
                        select.select_by_visible_text(option.text)
                        print(f"   ✅ 选择船公司: {option.text}")
                        found = True
                        break
                
                if not found:
                    print(f"   ❌ 未找到船公司: {selected_carrier}")
                    return False
            
            # 4.2 输入订舱号
            print("   4.2 输入订舱号...")
            so_input = driver.find_element(By.ID, "txtBookingNo")
            so_input.clear()
            so_input.send_keys(so_number)
            print(f"   ✅ 输入订舱号: {so_number}")
            
            # 4.3 点击查询
            print("   4.3 点击查询...")
            query_button = driver.find_element(By.ID, "imgQuery")
            query_button.click()
            print("   ✅ 查询按钮已点击")
            
            # 等待查询结果
            print("   ⏳ 等待查询结果...")
            time.sleep(5)
            
        except Exception as e:
            print(f"   ❌ 执行打单操作失败: {e}")
            return False
        
        # 步骤5: 检查查询结果
        print("\n🔍 步骤5: 检查查询结果...")
        
        try:
            # 检查页面是否有变化
            new_url = driver.current_url
            print(f"   查询后URL: {new_url}")
            
            # 查找可能的结果表格或区域
            tables = driver.find_elements(By.TAG_NAME, "table")
            print(f"   页面表格数量: {len(tables)}")
            
            # 查找包含结果的表格
            result_found = False
            for i, table in enumerate(tables):
                try:
                    table_text = table.text.strip()
                    if table_text and any(keyword in table_text for keyword in ['单据地点', '提柜地点', '费用', '缴费']):
                        print(f"   ✅ 找到结果表格 {i+1}")
                        print(f"   📋 表格内容预览: {table_text[:200]}...")
                        result_found = True
                        break
                except:
                    continue
            
            if result_found:
                print("   ✅ 查询成功，找到结果")
                
                # 查找提柜地点选择
                print("\n   🔍 查找提柜地点选择...")
                
                # 查找可能的地点选择下拉框
                selects = driver.find_elements(By.TAG_NAME, "select")
                location_selects = []
                
                for select_elem in selects:
                    try:
                        # 排除已知的船公司选择框
                        if select_elem.get_attribute("id") != "ddlCarrier_DropDownList1":
                            select_obj = Select(select_elem)
                            options = [option.text for option in select_obj.options]
                            
                            # 检查是否包含地点相关的选项
                            if any(keyword in ' '.join(options) for keyword in ['码头', '堆场', '地点', 'Terminal', 'Yard']):
                                location_selects.append((select_elem, options))\n                                print(f\"   ✅ 找到地点选择框: {options}\")\n                    except:\n                        continue\n                \n                # 如果找到地点选择框，优先选择码头\n                if location_selects:\n                    for select_elem, options in location_selects:\n                        select_obj = Select(select_elem)\n                        \n                        # 优先选择码头\n                        for option in select_obj.options:\n                            if '码头' in option.text and '堆场' not in option.text:\n                                select_obj.select_by_visible_text(option.text)\n                                print(f\"   ✅ 选择码头: {option.text}\")\n                                break\n                        else:\n                            # 如果没有码头，选择第一个非空选项\n                            for option in select_obj.options:\n                                if option.text.strip():\n                                    select_obj.select_by_visible_text(option.text)\n                                    print(f\"   ✅ 选择地点: {option.text}\")\n                                    break\n                \n                # 查找缴费按钮\n                print(\"\\n   🔍 查找缴费按钮...\")\n                \n                pay_buttons = driver.find_elements(By.XPATH, \"//input[contains(@value, '缴费')] | //button[contains(text(), '缴费')]\")\n                \n                if pay_buttons:\n                    print(f\"   ✅ 找到 {len(pay_buttons)} 个缴费按钮\")\n                    \n                    # 询问是否继续缴费\n                    choice = input(\"\\n是否继续点击缴费按钮？(y/n): \").strip().lower()\n                    \n                    if choice == 'y':\n                        print(\"   🔄 点击缴费按钮...\")\n                        pay_buttons[0].click()\n                        time.sleep(3)\n                        \n                        # 查找确认支付按钮\n                        print(\"   🔍 查找确认支付按钮...\")\n                        confirm_buttons = driver.find_elements(By.XPATH, \"//input[contains(@value, '确认支付')] | //button[contains(text(), '确认支付')]\")\n                        \n                        if confirm_buttons:\n                            print(\"   ✅ 找到确认支付按钮\")\n                            print(\"   💡 可以继续支付流程\")\n                            \n                            # 询问是否确认支付\n                            confirm_choice = input(\"   是否点击确认支付？(y/n): \").strip().lower()\n                            \n                            if confirm_choice == 'y':\n                                print(\"   💳 点击确认支付...\")\n                                confirm_buttons[0].click()\n                                time.sleep(3)\n                                print(\"   ✅ 确认支付按钮已点击\")\n                            else:\n                                print(\"   ⏭️ 跳过确认支付\")\n                        else:\n                            print(\"   ⚠️ 未找到确认支付按钮\")\n                    else:\n                        print(\"   ⏭️ 跳过缴费步骤\")\n                else:\n                    print(\"   ❌ 未找到缴费按钮\")\n                \n                return True\n            else:\n                print(\"   ❌ 查询无结果或页面无明显变化\")\n                \n                # 检查是否有错误信息\n                error_elements = driver.find_elements(By.XPATH, \"//*[contains(text(), '错误') or contains(text(), '失败') or contains(text(), '无效')]\")\n                if error_elements:\n                    for elem in error_elements:\n                        try:\n                            error_text = elem.text.strip()\n                            if error_text:\n                                print(f\"   ⚠️ 错误信息: {error_text}\")\n                        except:\n                            pass\n                \n                return False\n                \n        except Exception as e:\n            print(f\"   ❌ 检查查询结果失败: {e}\")\n            return False\n        \n    except Exception as e:\n        print(f\"❌ 完整打单流程测试失败: {e}\")\n        import traceback\n        traceback.print_exc()\n        return False\n    \n    finally:\n        # 询问是否保持浏览器打开\n        try:\n            choice = input(\"\\n是否保持浏览器打开以便查看结果？(y/n): \").strip().lower()\n            if choice == 'y':\n                print(\"浏览器将保持打开状态...\")\n                input(\"按 Enter 键关闭浏览器...\")\n            \n            eir_service.close()\n        except:\n            pass\n\ndef main():\n    \"\"\"主函数\"\"\"\n    print(\"🎯 EIR完整打单功能测试\")\n    print(\"=\" * 60)\n    \n    choice = input(\"\"\"\n选择测试模式:\n1. 完整打单流程测试 (推荐)\n2. 退出\n请输入选择 (1-2): \"\"\").strip()\n    \n    if choice == \"1\":\n        print(\"\\n🚀 开始完整打单流程测试...\")\n        success = asyncio.run(test_complete_order_process())\n        \n        if success:\n            print(\"\\n🎉 完整打单流程测试成功！\")\n            print(\"✅ EIR打单功能已验证可用\")\n        else:\n            print(\"\\n⚠️ 打单流程测试遇到问题\")\n            print(\"💡 请检查输入的船公司和订舱号是否正确\")\n    \n    elif choice == \"2\":\n        print(\"👋 退出测试\")\n        return\n    \n    else:\n        print(\"无效选择，退出\")\n\nif __name__ == \"__main__\":\n    try:\n        main()\n    except KeyboardInterrupt:\n        print(\"\\n\\n⚠️ 用户中断测试\")\n    except Exception as e:\n        print(f\"\\n❌ 程序发生错误: {e}\")\n        import traceback\n        traceback.print_exc()"
