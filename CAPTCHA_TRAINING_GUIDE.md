# 验证码训练数据收集和模型训练指南

## 🎯 概述

本指南将帮助您完成验证码训练数据的收集、标注和模型训练的完整流程，以提高验证码识别的准确率。

## 📋 准备工作

### 1. 环境要求
- Python 3.8+
- Chrome浏览器
- 网络连接（支持代理）
- 足够的磁盘空间（约1GB用于1000个验证码）

### 2. 依赖安装
```bash
# 安装额外的机器学习依赖
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ scikit-learn joblib
```

## 🚀 完整流程

### 第一步：批量收集验证码

#### 快速开始（推荐）
```bash
# 收集100个验证码进行快速测试
python captcha_training_manager.py quick --count 100

# 收集1000个验证码（完整训练集）
python captcha_training_manager.py quick --count 1000
```

#### 分步操作
```bash
# 1. 仅收集验证码
python captcha_training_manager.py collect --count 1000 --batch 50

# 2. 查看收集统计
python captcha_training_manager.py stats

# 3. 标注验证码
python captcha_training_manager.py label --batch 50

# 4. 导出训练数据
python captcha_training_manager.py export
```

### 第二步：标注验证码

#### 交互式标注
运行标注命令后，系统会：
1. 显示验证码图片（放大4倍便于查看）
2. 显示图片信息和特征
3. 等待用户输入

#### 标注操作说明
- **输入4位数字**: 进行标注（如：1234）
- **输入 's'**: 跳过当前验证码
- **输入 'q'**: 退出标注
- **输入 'stats'**: 查看当前统计信息

#### 标注示例
```
进度: 1/100

验证码信息:
文件名: captcha_20241212_120001_001_1752325000.png
尺寸: 50x22
文件大小: 2006 bytes
平均亮度: 128.5
边缘密度: 0.234
黑色像素比例: 0.355

图片路径: data/captcha_collection/captcha_20241212_120001_001_1752325000.png
请输入验证码内容 (4位数字): 1939
```

### 第三步：训练模型

#### 训练验证码识别模型
```bash
# 训练所有模型
python train_captcha_model.py train
```

训练过程会：
1. 加载标注数据
2. 预处理图片（调整尺寸、二值化、归一化）
3. 提取特征向量
4. 训练5个模型：
   - 4个分位置数字模型（每个位置一个）
   - 1个整体验证码模型
5. 评估模型性能
6. 保存模型文件

#### 查看模型信息
```bash
# 显示模型详细信息
python train_captcha_model.py info
```

#### 测试模型
```bash
# 测试单个图片
python train_captcha_model.py test --image data/captcha_collection/captcha_xxx.png

# 批量测试模型性能
python train_captcha_model.py batch_test
```

## 📊 数据管理

### 目录结构
```
data/
├── captcha_collection/          # 原始验证码收集
│   ├── captcha_xxx.png         # 验证码图片
│   └── metadata.json           # 收集元数据
├── captcha_labeled/             # 标注数据
│   ├── labels.json             # 标注信息
│   └── training_data.json      # 训练数据
└── models/                      # 训练好的模型
    ├── digit_model_position_0.joblib  # 第1位数字模型
    ├── digit_model_position_1.joblib  # 第2位数字模型
    ├── digit_model_position_2.joblib  # 第3位数字模型
    ├── digit_model_position_3.joblib  # 第4位数字模型
    ├── whole_captcha_model.joblib     # 整体模型
    └── training_info.json             # 训练信息
```

### 统计信息查看
```bash
# 查看收集和标注统计
python captcha_training_manager.py stats
```

输出示例：
```
📊 验证码训练数据统计

🗂️  收集统计:
累计收集: 1000 个验证码
收集会话: 3 次
成功率: 950 / 1000
文件大小: 1500-2500 bytes (平均: 2000)
图片尺寸: 50-50 x 22-22
常见尺寸: 50x22

🏷️  标注统计:
已标注: 800 个
未标注: 200 个
标注进度: 80.0%
标注会话: 5 次

🔢 数字频率分布:
  0: 195 次
  1: 201 次
  2: 189 次
  ...

📏 长度分布:
  4位: 800 个
```

## 🎯 最佳实践

### 1. 收集策略
- **分批收集**: 每次50-100个，避免被网站检测
- **时间分散**: 不同时间段收集，获得更多样的验证码
- **质量检查**: 定期检查收集的图片质量

### 2. 标注策略
- **准确性优先**: 宁可跳过也不要标注错误
- **一致性**: 保持标注标准一致
- **分批标注**: 每次标注50-100个，避免疲劳

### 3. 训练策略
- **数据量**: 建议至少500个标注样本
- **数据平衡**: 确保每个数字都有足够的样本
- **模型验证**: 使用批量测试验证模型性能

## 🔧 高级功能

### 1. 自定义收集参数
```bash
# 自定义批次大小和延迟
python captcha_training_manager.py collect --count 500 --batch 25
```

### 2. 模型性能优化
- 调整图片预处理参数
- 修改模型超参数
- 增加特征工程

### 3. 集成到自动登录
训练完成后，可以将模型集成到自动登录系统中：

```python
from backend.app.services.captcha_trainer import captcha_trainer

# 加载训练好的模型
models = captcha_trainer.load_trained_models()

# 预测验证码
result = captcha_trainer.predict_captcha("captcha.png", models)
predicted_code = result.get("best_prediction")
```

## 📈 性能指标

### 目标性能
- **收集成功率**: >90%
- **标注准确率**: >95%
- **模型准确率**: >80%（分位置模型）
- **整体准确率**: >75%（完整验证码）

### 性能监控
```bash
# 定期检查模型性能
python train_captcha_model.py batch_test
```

## 🚨 注意事项

### 1. 网络和代理
- 确保代理设置正确（127.0.0.1:7897）
- 网络不稳定时适当增加延迟

### 2. 资源管理
- 定期清理临时文件
- 监控磁盘空间使用

### 3. 数据安全
- 备份重要的标注数据
- 不要删除原始收集的验证码

## 🎉 完成后的效果

完成训练后，您将获得：
1. **高精度验证码识别模型**
2. **完整的训练数据集**
3. **可重复的训练流程**
4. **性能评估报告**

这些模型可以直接集成到EIR自动登录系统中，大幅提高验证码识别的成功率！

## 📞 故障排除

### 常见问题
1. **Chrome启动失败**: 检查ChromeDriver版本
2. **网络连接超时**: 检查代理设置
3. **图片显示问题**: 安装OpenCV依赖
4. **模型训练内存不足**: 减少批次大小

### 获取帮助
```bash
# 查看命令帮助
python captcha_training_manager.py --help
python train_captcha_model.py --help
```
