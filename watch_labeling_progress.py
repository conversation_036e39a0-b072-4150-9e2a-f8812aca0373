#!/usr/bin/env python3
"""
实时监控标注进度
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def get_current_progress():
    """获取当前进度"""
    labels_file = "data/captcha_labeled/labels.json"
    collection_dir = "data/captcha_collection"
    
    # 获取总验证码数量
    total_count = 0
    if os.path.exists(collection_dir):
        total_count = len([f for f in os.listdir(collection_dir) if f.endswith('.png')])
    
    # 获取已标注数量
    labeled_count = 0
    if os.path.exists(labels_file):
        try:
            with open(labels_file, 'r', encoding='utf-8') as f:
                labels_data = json.load(f)
            labeled_count = len(labels_data.get("labels", {}))
        except:
            pass
    
    return total_count, labeled_count

def print_progress_bar(current, total, width=40):
    """打印进度条"""
    if total == 0:
        return "░" * width
    
    filled = int(width * current / total)
    bar = "█" * filled + "░" * (width - filled)
    return bar

def main():
    """主函数"""
    print("🔄 实时监控验证码标注进度")
    print("按 Ctrl+C 停止监控")
    print("=" * 50)
    
    last_labeled = 0
    start_time = time.time()
    
    try:
        while True:
            # 清屏
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # 获取当前进度
            total, labeled = get_current_progress()
            remaining = total - labeled
            progress_percent = (labeled / total * 100) if total > 0 else 0
            
            # 计算速度
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                speed = (labeled - last_labeled) / elapsed_time * 60  # 每分钟标注数
            else:
                speed = 0
            
            # 显示进度
            print("🔄 实时验证码标注进度监控")
            print("=" * 50)
            print(f"📅 当前时间: {datetime.now().strftime('%H:%M:%S')}")
            print(f"📦 总验证码: {total} 个")
            print(f"✅ 已标注: {labeled} 个")
            print(f"⏳ 待标注: {remaining} 个")
            print(f"📈 完成度: {progress_percent:.1f}%")
            
            # 进度条
            bar = print_progress_bar(labeled, total)
            print(f"📊 进度条: [{bar}] {progress_percent:.1f}%")
            
            # 速度和预估
            if speed > 0:
                eta_minutes = remaining / speed
                print(f"⚡ 标注速度: {speed:.1f} 个/分钟")
                print(f"⏰ 预计完成: {eta_minutes:.1f} 分钟")
            else:
                print(f"⚡ 标注速度: 计算中...")
                print(f"⏰ 预计完成: 计算中...")
            
            # 里程碑提醒
            print(f"\n🎯 里程碑:")
            milestones = [50, 80, 100]
            for milestone in milestones:
                if labeled < milestone:
                    remaining_to_milestone = milestone - labeled
                    status = "🎯 下一目标"
                    print(f"   {status}: {milestone} 个 (还需 {remaining_to_milestone} 个)")
                    break
                else:
                    print(f"   ✅ 已达成: {milestone} 个")
            
            # 数字分布提醒
            if labeled >= 10:  # 有足够数据时才显示
                try:
                    labels_file = "data/captcha_labeled/labels.json"
                    if os.path.exists(labels_file):
                        with open(labels_file, 'r', encoding='utf-8') as f:
                            labels_data = json.load(f)
                        
                        digit_stats = {str(i): 0 for i in range(10)}
                        for label_info in labels_data.get("labels", {}).values():
                            label = label_info.get("label", "")
                            for digit in label:
                                if digit in digit_stats:
                                    digit_stats[digit] += 1
                        
                        min_count = min(digit_stats.values())
                        min_digits = [d for d, c in digit_stats.items() if c == min_count]
                        
                        print(f"\n🔢 数字分布提醒:")
                        print(f"   最少出现: {min_count} 次 (数字: {', '.join(min_digits)})")
                        if min_count < 5:
                            print(f"   💡 建议: 多标注包含数字 {', '.join(min_digits)} 的验证码")
                except:
                    pass
            
            print(f"\n⏰ 下次更新: 5秒后... (Ctrl+C 停止)")
            
            # 检查是否有新标注
            if labeled > last_labeled:
                new_count = labeled - last_labeled
                print(f"🎉 新增标注: {new_count} 个!")
                last_labeled = labeled
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        print(f"\n\n✅ 监控已停止")
        print(f"📊 最终进度: {labeled}/{total} ({progress_percent:.1f}%)")

if __name__ == "__main__":
    main()
