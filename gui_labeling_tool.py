#!/usr/bin/env python3
"""
基于Tkinter的验证码标注GUI工具
"""

import sys
import os
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import cv2
import numpy as np
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class CaptchaLabelingGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("验证码标注工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 数据相关
        self.collection_dir = "data/captcha_collection"
        self.labels_file = "data/captcha_labeled/labels.json"
        self.labels_dir = "data/captcha_labeled"
        
        # 创建标注目录
        os.makedirs(self.labels_dir, exist_ok=True)
        
        # 加载数据
        self.labels = self.load_labels()
        self.unlabeled_files = self.get_unlabeled_files()
        self.current_index = 0
        
        # GUI变量
        self.current_image = None
        self.photo = None
        
        # 创建界面
        self.create_widgets()
        self.load_current_image()
        
        # 绑定键盘事件
        self.root.bind('<Return>', lambda e: self.submit_label())
        self.root.bind('<Right>', lambda e: self.next_image())
        self.root.bind('<Left>', lambda e: self.prev_image())
        self.root.bind('<Escape>', lambda e: self.skip_current())
        
        # 设置焦点到输入框
        self.entry_var.set("")
        self.label_entry.focus_set()
    
    def load_labels(self):
        """加载标注数据"""
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "labeled_count": 0,
            "labels": {},
            "statistics": {
                "digit_frequency": {str(i): 0 for i in range(10)},
                "sessions": []
            }
        }
    
    def save_labels(self):
        """保存标注数据"""
        try:
            with open(self.labels_file, 'w', encoding='utf-8') as f:
                json.dump(self.labels, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存标注失败: {e}")
            return False
    
    def get_unlabeled_files(self):
        """获取未标注的文件列表"""
        if not os.path.exists(self.collection_dir):
            return []
        
        all_files = [f for f in os.listdir(self.collection_dir) if f.endswith('.png')]
        labeled_files = set(self.labels.get("labels", {}).keys())
        
        unlabeled = [f for f in all_files if f not in labeled_files]
        return sorted(unlabeled)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题和统计信息
        self.create_header(main_frame)
        
        # 图片显示区域
        self.create_image_area(main_frame)
        
        # 输入区域
        self.create_input_area(main_frame)
        
        # 按钮区域
        self.create_button_area(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(header_frame, text="🏷️ 验证码标注工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # 统计信息
        self.stats_var = tk.StringVar()
        stats_label = ttk.Label(header_frame, textvariable=self.stats_var, font=("Arial", 10))
        stats_label.grid(row=0, column=1, sticky=tk.E)
        
        self.update_stats()
    
    def create_image_area(self, parent):
        """创建图片显示区域"""
        image_frame = ttk.LabelFrame(parent, text="验证码图片", padding="10")
        image_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)
        
        # 图片标签
        self.image_label = ttk.Label(image_frame, text="正在加载图片...", anchor="center")
        self.image_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件名标签
        self.filename_var = tk.StringVar()
        filename_label = ttk.Label(image_frame, textvariable=self.filename_var, font=("Arial", 9), foreground="gray")
        filename_label.grid(row=1, column=0, pady=(5, 0))
    
    def create_input_area(self, parent):
        """创建输入区域"""
        input_frame = ttk.LabelFrame(parent, text="输入验证码", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)
        
        # 输入提示
        ttk.Label(input_frame, text="验证码:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        # 输入框
        self.entry_var = tk.StringVar()
        self.label_entry = ttk.Entry(input_frame, textvariable=self.entry_var, font=("Arial", 14), width=10)
        self.label_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 输入验证
        self.entry_var.trace('w', self.validate_input)
        
        # 提示信息
        hint_label = ttk.Label(input_frame, text="请输入4位数字", font=("Arial", 9), foreground="gray")
        hint_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
    
    def create_button_area(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮样式
        style = ttk.Style()
        style.configure("Success.TButton", foreground="green")
        style.configure("Warning.TButton", foreground="orange")
        style.configure("Info.TButton", foreground="blue")
        
        # 提交按钮
        self.submit_btn = ttk.Button(button_frame, text="✅ 提交 (Enter)", command=self.submit_label, style="Success.TButton")
        self.submit_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 跳过按钮
        skip_btn = ttk.Button(button_frame, text="⏭️ 跳过 (Esc)", command=self.skip_current, style="Warning.TButton")
        skip_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 上一张按钮
        prev_btn = ttk.Button(button_frame, text="⬅️ 上一张 (←)", command=self.prev_image)
        prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 下一张按钮
        next_btn = ttk.Button(button_frame, text="➡️ 下一张 (→)", command=self.next_image)
        next_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 统计按钮
        stats_btn = ttk.Button(button_frame, text="📊 统计", command=self.show_detailed_stats, style="Info.TButton")
        stats_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 保存按钮
        save_btn = ttk.Button(button_frame, text="💾 保存", command=self.save_labels)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        # 进度信息
        self.progress_var = tk.StringVar()
        progress_label = ttk.Label(status_frame, textvariable=self.progress_var, font=("Arial", 9))
        progress_label.grid(row=0, column=0, sticky=tk.W)
        
        # 快捷键提示
        shortcut_label = ttk.Label(status_frame, text="快捷键: Enter=提交 | Esc=跳过 | ←→=切换", 
                                 font=("Arial", 9), foreground="gray")
        shortcut_label.grid(row=0, column=1, sticky=tk.E)
    
    def validate_input(self, *args):
        """验证输入"""
        text = self.entry_var.get()
        
        # 只允许数字，最多4位
        if text and not text.isdigit():
            self.entry_var.set(''.join(c for c in text if c.isdigit()))
        
        if len(text) > 4:
            self.entry_var.set(text[:4])
        
        # 更新提交按钮状态
        if len(self.entry_var.get()) == 4:
            self.submit_btn.configure(state="normal")
        else:
            self.submit_btn.configure(state="disabled")
    
    def load_current_image(self):
        """加载当前图片"""
        if not self.unlabeled_files or self.current_index >= len(self.unlabeled_files):
            self.show_completion_message()
            return
        
        filename = self.unlabeled_files[self.current_index]
        filepath = os.path.join(self.collection_dir, filename)
        
        try:
            # 使用OpenCV读取图片
            img = cv2.imread(filepath)
            if img is None:
                raise Exception("无法读取图片")
            
            # 转换颜色空间
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # 放大图片以便查看
            scale = 8
            height, width = img_rgb.shape[:2]
            enlarged = cv2.resize(img_rgb, (width * scale, height * scale), interpolation=cv2.INTER_NEAREST)
            
            # 转换为PIL图片
            pil_image = Image.fromarray(enlarged)
            
            # 转换为Tkinter可用的格式
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 更新图片标签
            self.image_label.configure(image=self.photo, text="")
            
            # 更新文件名
            self.filename_var.set(f"文件: {filename}")
            
            # 更新进度
            self.update_progress()
            
            # 清空输入框并设置焦点
            self.entry_var.set("")
            self.label_entry.focus_set()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {e}")
            self.image_label.configure(image="", text=f"无法加载图片\n{filename}")
    
    def update_progress(self):
        """更新进度信息"""
        total = len(self.unlabeled_files)
        current = self.current_index + 1
        
        if total > 0:
            progress_text = f"进度: {current}/{total} ({current/total*100:.1f}%)"
        else:
            progress_text = "没有待标注的图片"
        
        self.progress_var.set(progress_text)
    
    def update_stats(self):
        """更新统计信息"""
        labeled_count = len(self.labels.get("labels", {}))
        unlabeled_count = len(self.unlabeled_files)
        total_count = labeled_count + unlabeled_count
        
        if total_count > 0:
            progress_pct = labeled_count / total_count * 100
            stats_text = f"已标注: {labeled_count} | 待标注: {unlabeled_count} | 进度: {progress_pct:.1f}%"
        else:
            stats_text = "没有验证码数据"
        
        self.stats_var.set(stats_text)
    
    def submit_label(self):
        """提交标注"""
        label = self.entry_var.get().strip()
        
        if len(label) != 4 or not label.isdigit():
            messagebox.showwarning("输入错误", "请输入4位数字")
            return
        
        if not self.unlabeled_files or self.current_index >= len(self.unlabeled_files):
            return
        
        filename = self.unlabeled_files[self.current_index]
        
        # 添加标注
        self.labels["labels"][filename] = {
            "label": label,
            "labeled_time": datetime.now().isoformat()
        }
        
        # 更新统计
        self.labels["labeled_count"] = len(self.labels["labels"])
        
        for digit in label:
            if digit in self.labels["statistics"]["digit_frequency"]:
                self.labels["statistics"]["digit_frequency"][digit] += 1
        
        # 保存数据
        self.save_labels()
        
        # 从未标注列表中移除
        self.unlabeled_files.pop(self.current_index)
        
        # 更新统计显示
        self.update_stats()
        
        # 加载下一张图片
        if self.current_index >= len(self.unlabeled_files):
            self.current_index = max(0, len(self.unlabeled_files) - 1)
        
        self.load_current_image()
        
        # 显示成功消息
        self.root.title(f"验证码标注工具 - 已标注: {label}")
        self.root.after(2000, lambda: self.root.title("验证码标注工具"))
    
    def skip_current(self):
        """跳过当前图片"""
        self.next_image()
    
    def next_image(self):
        """下一张图片"""
        if self.unlabeled_files and self.current_index < len(self.unlabeled_files) - 1:
            self.current_index += 1
            self.load_current_image()
    
    def prev_image(self):
        """上一张图片"""
        if self.current_index > 0:
            self.current_index -= 1
            self.load_current_image()
    
    def show_detailed_stats(self):
        """显示详细统计"""
        labeled_count = len(self.labels.get("labels", {}))
        unlabeled_count = len(self.unlabeled_files)
        total_count = labeled_count + unlabeled_count
        
        # 数字频率统计
        freq = self.labels.get("statistics", {}).get("digit_frequency", {})
        freq_text = "\n".join([f"{digit}: {count} 次" for digit, count in freq.items() if count > 0])
        
        stats_message = f"""📊 详细统计信息

总验证码数: {total_count}
已标注: {labeled_count}
待标注: {unlabeled_count}
完成度: {labeled_count/total_count*100:.1f}% (如果总数>0)

🔢 数字频率分布:
{freq_text if freq_text else "暂无数据"}
"""
        
        messagebox.showinfo("统计信息", stats_message)
    
    def show_completion_message(self):
        """显示完成消息"""
        self.image_label.configure(image="", text="🎉\n所有验证码已标注完成！\n\n可以开始训练模型了")
        self.filename_var.set("标注完成")
        self.progress_var.set("进度: 100%")
        
        # 禁用相关按钮
        self.submit_btn.configure(state="disabled")
        self.label_entry.configure(state="disabled")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = CaptchaLabelingGUI()
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
