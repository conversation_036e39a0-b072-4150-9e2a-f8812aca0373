#!/usr/bin/env python3
"""
快速API测试
"""

import requests
import json

def quick_test():
    """快速测试API"""
    print("🔍 快速API测试...")
    
    try:
        # 测试根路径
        print("测试根路径...")
        response = requests.get("http://localhost:8001/", timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"响应: {response.json()}")
            print("✅ API服务正常运行")
            return True
        else:
            print(f"❌ 响应异常: {response.text}")
            return False
            
    except requests.exceptions.ConnectError:
        print("❌ 无法连接到服务器，请确认服务器是否在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    quick_test()
