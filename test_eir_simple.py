#!/usr/bin/env python3
"""
简化的EIR登录测试
"""

import sys
import os
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_eir_login():
    """测试EIR登录功能"""
    print("=== EIR登录功能测试 ===")
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务...")
        eir_service = EIRService()
        
        print("2. 获取验证码...")
        result = await eir_service.get_login_captcha()
        print(f"验证码获取结果: {result}")
        
        if result["success"]:
            captcha_path = result["captcha_path"]
            print(f"✅ 验证码保存在: {captcha_path}")
            
            # 显示验证码图片（如果可能）
            if os.path.exists(captcha_path):
                print("📷 验证码图片已保存，请查看文件")
                
                # 用户输入验证码
                captcha_code = input("请输入验证码 (或按Enter跳过): ").strip()
                
                if captcha_code:
                    print(f"3. 使用验证码 '{captcha_code}' 登录...")
                    login_result = await eir_service.login(captcha_code)
                    print(f"登录结果: {login_result}")
                    
                    if login_result["success"]:
                        print("🎉 登录成功！")
                    else:
                        print(f"❌ 登录失败: {login_result['message']}")
                else:
                    print("⏭️  跳过登录测试")
            else:
                print("❌ 验证码图片文件不存在")
        else:
            print(f"❌ 验证码获取失败: {result['message']}")
        
        # 清理资源
        eir_service.close()
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_eir_login())
