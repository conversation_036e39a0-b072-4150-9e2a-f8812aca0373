#!/usr/bin/env python3
"""
4位数字验证码自动登录测试
"""

import sys
import os
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_digit_recognition():
    """测试4位数字验证码识别"""
    print("=== 4位数字验证码识别测试 ===")
    
    try:
        from backend.app.services.captcha_service_digits import digit_captcha_service
        
        # 检查现有验证码图片
        captcha_dir = "data/captcha"
        if os.path.exists(captcha_dir):
            captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
            if captcha_files:
                # 测试所有验证码图片
                for captcha_file in captcha_files:
                    captcha_path = os.path.join(captcha_dir, captcha_file)
                    print(f"\n测试验证码: {captcha_file}")
                    
                    result = digit_captcha_service.recognize_captcha(captcha_path)
                    print(f"识别结果: {result}")
                    
                    if result["success"]:
                        print(f"✅ 最佳识别: {result['best_result']} (置信度: {result['confidence']})")
                        print(f"✅ 所有候选: {result['all_results']}")
                        if 'recognized_digits' in result:
                            print(f"✅ 识别数字: {result['recognized_digits']}")
                    else:
                        print(f"❌ 识别失败: {result['message']}")
            else:
                print("❌ 没有找到验证码图片")
        else:
            print("❌ 验证码目录不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_auto_login():
    """测试完全自动登录"""
    print("\n=== 完全自动登录测试 ===")
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务...")
        eir_service = EIRService()
        
        print("2. 开始完全自动登录...")
        print("   - 自动获取验证码")
        print("   - 自动识别4位数字")
        print("   - 智能重试机制")
        print("   - 多候选尝试")
        
        result = await eir_service.auto_login()
        print(f"\n自动登录结果: {result}")
        
        if result["success"]:
            print("🎉 完全自动登录成功！")
            print("✅ 系统已自动完成:")
            print("   - 验证码下载")
            print("   - 4位数字识别")
            print("   - 登录提交")
            print("   - Cookie保存")
            
            # 验证登录状态
            print("\n3. 验证登录状态...")
            status_result = await eir_service.check_login_status()
            print(f"登录状态: {status_result}")
            
        else:
            print(f"❌ 自动登录失败: {result['message']}")
            print("\n可能的原因:")
            print("1. 验证码识别准确率需要提升")
            print("2. 网络连接问题")
            print("3. EIR网站结构变化")
            print("4. 账号密码问题")
        
        # 清理资源
        eir_service.close()
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 自动登录测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_new_captcha_and_login():
    """获取新验证码并测试自动登录"""
    print("\n=== 新验证码自动登录测试 ===")
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.app.services.captcha_service_digits import digit_captcha_service
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务...")
        eir_service = EIRService()
        
        print("2. 获取新验证码...")
        captcha_result = await eir_service.get_login_captcha()
        print(f"验证码获取结果: {captcha_result}")
        
        if captcha_result["success"]:
            captcha_path = captcha_result["captcha_path"]
            print(f"✅ 验证码保存在: {captcha_path}")
            
            print("3. 测试4位数字识别...")
            recognition_result = digit_captcha_service.recognize_captcha(captcha_path)
            print(f"识别结果: {recognition_result}")
            
            if recognition_result["success"]:
                best_result = recognition_result["best_result"]
                all_results = recognition_result["all_results"]
                confidence = recognition_result["confidence"]
                
                print(f"✅ 最佳识别: {best_result}")
                print(f"✅ 置信度: {confidence}")
                print(f"✅ 所有候选: {all_results}")
                
                print("\n4. 开始智能自动登录...")
                print("   系统将自动尝试所有候选验证码")
                
                login_result = await eir_service.auto_login()
                print(f"自动登录结果: {login_result}")
                
                if login_result["success"]:
                    print("🎉 智能自动登录成功！")
                    
                    # 分析成功的验证码
                    success_msg = login_result["message"]
                    print(f"成功信息: {success_msg}")
                    
                else:
                    print(f"❌ 智能自动登录失败: {login_result['message']}")
                    
                    # 提供手动验证选项
                    print("\n手动验证选项:")
                    for i, candidate in enumerate(all_results, 1):
                        print(f"{i}. {candidate}")
                    
                    choice = input(f"\n请选择正确的验证码 (1-{len(all_results)}) 或输入正确答案: ").strip()
                    
                    if choice.isdigit() and 1 <= int(choice) <= len(all_results):
                        correct_code = all_results[int(choice) - 1]
                    elif choice and len(choice) == 4 and choice.isdigit():
                        correct_code = choice
                    else:
                        print("跳过手动验证")
                        return
                    
                    print(f"使用验证码 '{correct_code}' 进行手动登录...")
                    manual_result = await eir_service.login(correct_code)
                    print(f"手动登录结果: {manual_result}")
                    
                    # 分析识别准确性
                    print(f"\n识别准确性分析:")
                    print(f"系统识别: {best_result}")
                    print(f"正确答案: {correct_code}")
                    print(f"准确性: {'✅ 正确' if correct_code == best_result else '❌ 错误'}")
                    
                    if correct_code in all_results:
                        index = all_results.index(correct_code) + 1
                        print(f"✅ 正确答案在候选列表第 {index} 位")
                    else:
                        print(f"❌ 正确答案不在候选列表中")
                        
            else:
                print(f"❌ 验证码识别失败: {recognition_result['message']}")
        else:
            print(f"❌ 验证码获取失败: {captcha_result['message']}")
        
        # 清理资源
        eir_service.close()
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始4位数字验证码自动登录测试...")
    
    choice = input("""
选择测试模式:
1. 测试现有验证码图片识别
2. 完全自动登录测试
3. 获取新验证码并测试自动登录
请输入选择 (1/2/3): """).strip()
    
    if choice == "1":
        asyncio.run(test_digit_recognition())
    elif choice == "2":
        asyncio.run(test_auto_login())
    elif choice == "3":
        asyncio.run(test_new_captcha_and_login())
    else:
        print("无效选择，运行完整测试")
        asyncio.run(test_digit_recognition())
        asyncio.run(test_auto_login())

if __name__ == "__main__":
    main()
