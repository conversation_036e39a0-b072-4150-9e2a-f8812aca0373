#!/usr/bin/env python3
"""
启动EIR登录测试服务器
"""

import sys
import os
import subprocess
import webbrowser
import time

def check_requirements():
    """检查并安装依赖"""
    print("检查Python依赖...")
    
    try:
        import selenium
        import fastapi
        import uvicorn
        import requests
        import PIL
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("正在安装依赖...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False

def check_chromedriver():
    """检查ChromeDriver"""
    print("检查ChromeDriver...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✅ ChromeDriver 可用")
        return True
    except Exception as e:
        print(f"❌ ChromeDriver 不可用: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        print("ChromeDriver下载地址: https://chromedriver.chromium.org/")
        return False

def start_server():
    """启动服务器"""
    print("启动FastAPI服务器...")
    
    # 添加backend目录到Python路径
    backend_path = os.path.join(os.path.dirname(__file__), 'backend')
    sys.path.insert(0, backend_path)
    
    try:
        import uvicorn
        from backend.app.main import app
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("服务器启动中...")
        print("API地址: http://localhost:8000")
        print("测试页面: frontend/index.html")
        print("按 Ctrl+C 停止服务器")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            frontend_path = os.path.join(os.path.dirname(__file__), 'frontend', 'index.html')
            if os.path.exists(frontend_path):
                webbrowser.open(f'file://{os.path.abspath(frontend_path)}')
        
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        # 启动服务器
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器失败: {e}")

def main():
    print("=== EIR登录测试系统启动 ===")
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 检查ChromeDriver
    if not check_chromedriver():
        print("警告: ChromeDriver不可用，某些功能可能无法正常工作")
        response = input("是否继续启动服务器？(y/n): ").strip().lower()
        if response != 'y':
            return
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
