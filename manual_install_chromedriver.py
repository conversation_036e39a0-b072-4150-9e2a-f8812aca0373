#!/usr/bin/env python3
"""
ChromeDriver手动安装指南和验证工具
"""

import os
import subprocess
import sys

def check_chrome_version():
    """检查Chrome版本"""
    try:
        # Windows方法1: 通过注册表
        import winreg
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
        version, _ = winreg.QueryValueEx(key, "version")
        winreg.CloseKey(key)
        return version
    except:
        pass
    
    try:
        # Windows方法2: 通过文件版本
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                cmd = f'(Get-Item "{path}").VersionInfo.FileVersion'
                result = subprocess.run(["powershell", "-Command", cmd], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    if version:
                        return version
    except:
        pass
    
    return None

def test_chromedriver():
    """测试ChromeDriver是否工作"""
    try:
        if not os.path.exists("chromedriver.exe"):
            return False, "chromedriver.exe不存在"
        
        result = subprocess.run(["chromedriver.exe", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            return True, version
        else:
            return False, result.stderr
            
    except Exception as e:
        return False, str(e)

def main():
    """主函数"""
    print("=== ChromeDriver手动安装指南 ===")
    
    # 检查Chrome版本
    chrome_version = check_chrome_version()
    if chrome_version:
        print(f"✅ 检测到Chrome版本: {chrome_version}")
        major_version = chrome_version.split('.')[0]
    else:
        print("❌ 未检测到Chrome浏览器")
        print("请先安装Chrome浏览器: https://www.google.com/chrome/")
        return
    
    # 检查现有ChromeDriver
    success, message = test_chromedriver()
    if success:
        print(f"✅ ChromeDriver已安装: {message}")
        print("可以直接使用！")
        return
    else:
        print(f"❌ ChromeDriver状态: {message}")
    
    # 提供下载指南
    print(f"\n📋 ChromeDriver手动安装步骤:")
    print(f"1. Chrome版本: {chrome_version} (主版本: {major_version})")
    print(f"2. 下载地址选择:")
    
    if int(major_version) >= 115:
        print(f"   🔗 Chrome 115+版本下载地址:")
        print(f"   https://googlechromelabs.github.io/chrome-for-testing/")
        print(f"   - 在页面中搜索版本 {major_version}")
        print(f"   - 下载 'chromedriver' 的 'win64' 版本")
    else:
        print(f"   🔗 Chrome 114及以下版本下载地址:")
        print(f"   https://chromedriver.storage.googleapis.com/index.html")
        print(f"   - 查找版本 {major_version}.x.x.x")
        print(f"   - 下载 chromedriver_win32.zip")
    
    print(f"\n3. 安装步骤:")
    print(f"   a) 下载对应版本的ChromeDriver")
    print(f"   b) 解压zip文件")
    print(f"   c) 将 chromedriver.exe 复制到项目根目录:")
    print(f"      {os.path.abspath('.')}")
    print(f"   d) 运行验证: python manual_install_chromedriver.py")
    
    print(f"\n4. 备选方案 - 使用webdriver-manager:")
    print(f"   pip install webdriver-manager")
    print(f"   然后在代码中使用自动管理")
    
    print(f"\n💡 提示:")
    print(f"   - 确保ChromeDriver版本与Chrome主版本号匹配")
    print(f"   - 将chromedriver.exe放在项目根目录或系统PATH中")
    print(f"   - 如果下载困难，可以使用国内镜像站")

if __name__ == "__main__":
    main()
