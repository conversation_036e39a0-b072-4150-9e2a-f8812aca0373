#!/usr/bin/env python3
"""
EIR登录调试脚本
实时查看页面状态和登录过程
"""

import sys
import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class EIRLoginDebugger:
    def __init__(self):
        self.driver = None
        self.base_url = "https://eir.cmclink.com"
        
    def init_driver(self):
        """初始化Chrome驱动"""
        try:
            chrome_options = Options()
            
            # 调试模式 - 不使用无头模式，可以看到浏览器
            # chrome_options.add_argument("--headless")  # 注释掉无头模式
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # 禁用图片加载以加快速度（可选）
            # prefs = {"profile.managed_default_content_settings.images": 2}
            # chrome_options.add_experimental_option("prefs", prefs)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            print("✅ Chrome浏览器启动成功")
            return True
            
        except Exception as e:
            print(f"❌ Chrome浏览器启动失败: {e}")
            return False
    
    def debug_page_load(self):
        """调试页面加载"""
        print("🔍 调试页面加载过程...")
        
        try:
            print(f"1. 访问URL: {self.base_url}")
            self.driver.get(self.base_url)
            
            print("2. 等待页面加载...")
            time.sleep(5)
            
            print(f"3. 当前URL: {self.driver.current_url}")
            print(f"4. 页面标题: {self.driver.title}")
            
            # 检查页面源码
            page_source = self.driver.page_source
            print(f"5. 页面源码长度: {len(page_source)} 字符")
            
            # 查找关键元素
            print("\n6. 查找登录表单元素...")
            
            # 查找用户名输入框
            try:
                username_elements = self.driver.find_elements(By.NAME, "txtUserName")
                if username_elements:
                    print("✅ 找到用户名输入框 (txtUserName)")
                else:
                    print("❌ 未找到用户名输入框 (txtUserName)")
                    # 尝试其他可能的名称
                    alt_usernames = ["username", "user", "loginname", "account"]
                    for alt_name in alt_usernames:
                        alt_elements = self.driver.find_elements(By.NAME, alt_name)
                        if alt_elements:
                            print(f"✅ 找到替代用户名输入框: {alt_name}")
                            break
            except Exception as e:
                print(f"❌ 查找用户名输入框异常: {e}")
            
            # 查找密码输入框
            try:
                password_elements = self.driver.find_elements(By.NAME, "txtPassword")
                if password_elements:
                    print("✅ 找到密码输入框 (txtPassword)")
                else:
                    print("❌ 未找到密码输入框 (txtPassword)")
                    # 尝试其他可能的名称
                    alt_passwords = ["password", "pwd", "pass"]
                    for alt_name in alt_passwords:
                        alt_elements = self.driver.find_elements(By.NAME, alt_name)
                        if alt_elements:
                            print(f"✅ 找到替代密码输入框: {alt_name}")
                            break
            except Exception as e:
                print(f"❌ 查找密码输入框异常: {e}")
            
            # 查找验证码输入框
            try:
                captcha_elements = self.driver.find_elements(By.NAME, "txtValidateCode")
                if captcha_elements:
                    print("✅ 找到验证码输入框 (txtValidateCode)")
                else:
                    print("❌ 未找到验证码输入框 (txtValidateCode)")
                    # 尝试其他可能的名称
                    alt_captchas = ["captcha", "validatecode", "verifycode", "code"]
                    for alt_name in alt_captchas:
                        alt_elements = self.driver.find_elements(By.NAME, alt_name)
                        if alt_elements:
                            print(f"✅ 找到替代验证码输入框: {alt_name}")
                            break
            except Exception as e:
                print(f"❌ 查找验证码输入框异常: {e}")
            
            # 查找登录按钮
            try:
                login_elements = self.driver.find_elements(By.NAME, "btnLogin")
                if login_elements:
                    print("✅ 找到登录按钮 (btnLogin)")
                else:
                    print("❌ 未找到登录按钮 (btnLogin)")
                    # 尝试其他可能的选择器
                    alt_selectors = [
                        (By.XPATH, "//input[@type='submit']"),
                        (By.XPATH, "//button[contains(text(), '登录')]"),
                        (By.XPATH, "//input[@value='登录']"),
                        (By.CLASS_NAME, "login-btn"),
                        (By.ID, "login"),
                        (By.ID, "submit")
                    ]
                    for selector_type, selector_value in alt_selectors:
                        try:
                            alt_elements = self.driver.find_elements(selector_type, selector_value)
                            if alt_elements:
                                print(f"✅ 找到替代登录按钮: {selector_type} = {selector_value}")
                                break
                        except:
                            continue
            except Exception as e:
                print(f"❌ 查找登录按钮异常: {e}")
            
            # 查找验证码图片
            try:
                captcha_img_elements = self.driver.find_elements(By.XPATH, "//img[contains(@src, 'ValidateCode') or contains(@src, 'captcha') or contains(@src, 'verifycode')]")
                if captcha_img_elements:
                    print("✅ 找到验证码图片")
                    for i, img in enumerate(captcha_img_elements):
                        src = img.get_attribute("src")
                        print(f"   验证码图片 {i+1}: {src}")
                else:
                    print("❌ 未找到验证码图片")
            except Exception as e:
                print(f"❌ 查找验证码图片异常: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 页面加载调试失败: {e}")
            return False
    
    def debug_form_interaction(self):
        """调试表单交互"""
        print("\n🖱️ 调试表单交互...")
        
        try:
            from backend.config import Config
            
            # 模拟填写表单
            print("1. 尝试填写用户名...")
            try:
                username_input = self.driver.find_element(By.NAME, "txtUserName")
                username_input.clear()
                username_input.send_keys(Config.EIR_USERNAME)
                print(f"✅ 用户名填写成功: {Config.EIR_USERNAME}")
            except Exception as e:
                print(f"❌ 用户名填写失败: {e}")
            
            print("2. 尝试填写密码...")
            try:
                password_input = self.driver.find_element(By.NAME, "txtPassword")
                password_input.clear()
                password_input.send_keys(Config.EIR_PASSWORD)
                print("✅ 密码填写成功")
            except Exception as e:
                print(f"❌ 密码填写失败: {e}")
            
            print("3. 尝试下载验证码...")
            try:
                # 查找验证码图片
                captcha_img = self.driver.find_element(By.XPATH, "//img[contains(@src, 'ValidateCode')]")
                captcha_src = captcha_img.get_attribute("src")
                print(f"✅ 验证码图片URL: {captcha_src}")
                
                # 下载验证码
                import requests
                response = requests.get(captcha_src, cookies={cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()})
                
                if response.status_code == 200:
                    captcha_path = "debug_captcha.png"
                    with open(captcha_path, 'wb') as f:
                        f.write(response.content)
                    print(f"✅ 验证码下载成功: {captcha_path}")
                    
                    # 使用机器学习识别验证码
                    print("4. 尝试识别验证码...")
                    from backend.app.services.captcha_service_ml import ml_captcha_service
                    
                    result = ml_captcha_service.recognize_captcha(captcha_path)
                    if result.get("success"):
                        prediction = result.get("best_result")
                        confidence = result.get("confidence", 0)
                        print(f"✅ 验证码识别成功: {prediction} (置信度: {confidence:.3f})")
                        
                        # 填写验证码
                        print("5. 尝试填写验证码...")
                        captcha_input = self.driver.find_element(By.NAME, "txtValidateCode")
                        captcha_input.clear()
                        captcha_input.send_keys(prediction)
                        print(f"✅ 验证码填写成功: {prediction}")
                        
                        # 询问是否执行登录
                        choice = input("\n是否执行登录操作？(y/n): ").strip().lower()
                        if choice == 'y':
                            print("6. 执行登录...")
                            login_button = self.driver.find_element(By.NAME, "btnLogin")
                            login_button.click()
                            
                            print("等待登录结果...")
                            time.sleep(5)
                            
                            # 检查登录结果
                            current_url = self.driver.current_url
                            page_title = self.driver.title
                            
                            print(f"登录后URL: {current_url}")
                            print(f"登录后标题: {page_title}")
                            
                            # 查找错误信息
                            try:
                                error_elements = self.driver.find_elements(By.XPATH, "//span[@id='lblMessage']")
                                if error_elements:
                                    error_msg = error_elements[0].text
                                    print(f"错误信息: {error_msg}")
                                else:
                                    print("未找到错误信息")
                            except:
                                print("无法获取错误信息")
                            
                            # 检查是否登录成功
                            if "login" not in current_url.lower() and current_url != self.base_url:
                                print("🎉 登录可能成功！")
                                return True
                            else:
                                print("❌ 登录可能失败")
                                return False
                        else:
                            print("跳过登录操作")
                            return True
                    else:
                        print(f"❌ 验证码识别失败: {result.get('message')}")
                        return False
                else:
                    print(f"❌ 验证码下载失败: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ 验证码处理失败: {e}")
                return False
            
        except Exception as e:
            print(f"❌ 表单交互调试失败: {e}")
            return False
    
    def debug_page_elements(self):
        """调试页面所有元素"""
        print("\n🔍 调试页面所有表单元素...")
        
        try:
            # 查找所有input元素
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            print(f"找到 {len(inputs)} 个input元素:")
            
            for i, input_elem in enumerate(inputs):
                try:
                    input_type = input_elem.get_attribute("type")
                    input_name = input_elem.get_attribute("name")
                    input_id = input_elem.get_attribute("id")
                    input_value = input_elem.get_attribute("value")
                    input_placeholder = input_elem.get_attribute("placeholder")
                    
                    print(f"  {i+1}. type={input_type}, name={input_name}, id={input_id}, value={input_value}, placeholder={input_placeholder}")
                except:
                    print(f"  {i+1}. 无法获取属性")
            
            # 查找所有button元素
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            print(f"\n找到 {len(buttons)} 个button元素:")
            
            for i, button in enumerate(buttons):
                try:
                    button_text = button.text
                    button_type = button.get_attribute("type")
                    button_name = button.get_attribute("name")
                    button_id = button.get_attribute("id")
                    
                    print(f"  {i+1}. text={button_text}, type={button_type}, name={button_name}, id={button_id}")
                except:
                    print(f"  {i+1}. 无法获取属性")
            
            # 查找所有img元素
            images = self.driver.find_elements(By.TAG_NAME, "img")
            print(f"\n找到 {len(images)} 个img元素:")
            
            for i, img in enumerate(images):
                try:
                    img_src = img.get_attribute("src")
                    img_alt = img.get_attribute("alt")
                    img_id = img.get_attribute("id")
                    
                    print(f"  {i+1}. src={img_src}, alt={img_alt}, id={img_id}")
                except:
                    print(f"  {i+1}. 无法获取属性")
            
            return True
            
        except Exception as e:
            print(f"❌ 页面元素调试失败: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("🔒 浏览器已关闭")

def main():
    """主函数"""
    print("🔧 EIR登录调试工具")
    print("=" * 50)
    
    debugger = EIRLoginDebugger()
    
    try:
        # 初始化浏览器
        if not debugger.init_driver():
            return
        
        choice = input("""
选择调试模式:
1. 页面加载调试
2. 表单交互调试
3. 页面元素调试
4. 完整调试流程 (推荐)
请输入选择 (1-4): """).strip()
        
        if choice == "1":
            debugger.debug_page_load()
        elif choice == "2":
            debugger.debug_page_load()
            debugger.debug_form_interaction()
        elif choice == "3":
            debugger.debug_page_load()
            debugger.debug_page_elements()
        elif choice == "4":
            print("🚀 执行完整调试流程...")
            debugger.debug_page_load()
            debugger.debug_page_elements()
            debugger.debug_form_interaction()
        else:
            print("无效选择，执行页面加载调试")
            debugger.debug_page_load()
        
        # 询问是否保持浏览器打开
        keep_open = input("\n是否保持浏览器打开以便手动检查？(y/n): ").strip().lower()
        if keep_open == 'y':
            print("浏览器将保持打开状态，按Enter键关闭...")
            input()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试过程发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        debugger.close()

if __name__ == "__main__":
    main()
