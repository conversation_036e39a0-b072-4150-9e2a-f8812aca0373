#!/usr/bin/env python3
"""
API接口测试脚本
"""

import requests
import json

API_BASE = "http://localhost:8001/api"

def test_api():
    """测试API接口"""
    print("=== EIR API接口测试 ===")
    
    # 1. 测试根路径
    print("\n1. 测试根路径...")
    try:
        response = requests.get("http://localhost:8001/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 2. 测试登录状态检查
    print("\n2. 测试登录状态检查...")
    try:
        response = requests.get(f"{API_BASE}/eir/status")
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 3. 测试获取验证码（这个会失败，因为没有ChromeDriver）
    print("\n3. 测试获取验证码...")
    try:
        response = requests.get(f"{API_BASE}/eir/captcha")
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 4. 测试API文档
    print("\n4. 测试API文档...")
    try:
        response = requests.get("http://localhost:8001/docs")
        print(f"状态码: {response.status_code}")
        print("API文档可访问" if response.status_code == 200 else "API文档不可访问")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_api()
