#!/usr/bin/env python3
"""
测试简单验证码收集器
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_simple_collection(count=10):
    """测试简单验证码收集"""
    print(f"🚀 测试简单验证码收集 ({count} 个)")
    
    try:
        from backend.app.services.simple_captcha_collector import simple_captcha_collector
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("开始收集验证码...")
        result = simple_captcha_collector.collect_captchas(count, 5)
        
        print(f"\n📊 收集结果:")
        print(f"状态: {result.get('status')}")
        print(f"成功: {result.get('success_count', 0)}")
        print(f"失败: {result.get('failed_count', 0)}")
        print(f"总计: {result.get('collected_count', 0)}")
        
        if result.get('status') == 'completed':
            print("✅ 收集完成！")
            
            # 显示统计信息
            stats = simple_captcha_collector.get_collection_statistics()
            print(f"\n📈 统计信息:")
            print(f"累计收集: {stats.get('total_collected', 0)} 个")
            
            if 'file_size_stats' in stats:
                fs = stats['file_size_stats']
                print(f"文件大小: {fs['min']}-{fs['max']} bytes (平均: {fs['mean']:.0f})")
            
            if 'image_stats' in stats:
                img = stats['image_stats']
                print(f"图片尺寸: {img['width_range'][0]}-{img['width_range'][1]} x {img['height_range'][0]}-{img['height_range'][1]}")
                print(f"常见尺寸: {img['common_size']}")
            
            return True
        else:
            print(f"⚠️ 收集未完成: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 收集失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 简单验证码收集测试")
    print("=" * 50)
    
    choice = input("""
选择收集数量:
1. 收集 5 个验证码
2. 收集 10 个验证码
3. 收集 20 个验证码
4. 收集 50 个验证码
请输入选择 (1/2/3/4): """).strip()
    
    count_map = {"1": 5, "2": 10, "3": 20, "4": 50}
    count = count_map.get(choice, 10)
    
    print(f"开始收集 {count} 个验证码...")
    success = test_simple_collection(count)
    
    if success:
        print(f"\n🎉 成功收集了验证码！")
        print(f"文件保存在: data/captcha_collection/")
        print(f"下一步可以运行标注工具进行标注")
    else:
        print(f"\n❌ 收集失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断收集")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
