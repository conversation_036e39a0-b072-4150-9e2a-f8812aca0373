#!/usr/bin/env python3
"""
简化的验证码标注工具
"""

import sys
import os
import json
import cv2
import numpy as np
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class SimpleCaptchaLabeler:
    def __init__(self):
        self.collection_dir = "data/captcha_collection"
        self.labels_file = "data/captcha_labeled/labels.json"
        self.labels_dir = "data/captcha_labeled"
        
        # 创建标注目录
        os.makedirs(self.labels_dir, exist_ok=True)
        
        # 加载已有标注
        self.labels = self.load_labels()
    
    def load_labels(self):
        """加载标注数据"""
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "labeled_count": 0,
            "labels": {},
            "statistics": {
                "digit_frequency": {str(i): 0 for i in range(10)},
                "sessions": []
            }
        }
    
    def save_labels(self):
        """保存标注数据"""
        try:
            with open(self.labels_file, 'w', encoding='utf-8') as f:
                json.dump(self.labels, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存标注失败: {e}")
    
    def get_unlabeled_files(self):
        """获取未标注的文件列表"""
        if not os.path.exists(self.collection_dir):
            return []
        
        all_files = [f for f in os.listdir(self.collection_dir) if f.endswith('.png')]
        labeled_files = set(self.labels.get("labels", {}).keys())
        
        unlabeled = [f for f in all_files if f not in labeled_files]
        return sorted(unlabeled)
    
    def display_image(self, filepath, filename):
        """显示验证码图片"""
        try:
            img = cv2.imread(filepath)
            if img is None:
                print("⚠️ 无法读取图片")
                return False
            
            # 放大图片
            scale = 10
            height, width = img.shape[:2]
            enlarged = cv2.resize(img, (width * scale, height * scale), interpolation=cv2.INTER_NEAREST)
            
            # 显示图片
            window_name = f"验证码 - {filename}"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
            cv2.imshow(window_name, enlarged)
            cv2.moveWindow(window_name, 200, 200)
            cv2.waitKey(1)
            
            # 同时显示ASCII版本
            self.print_ascii_art(img)
            
            return True
            
        except Exception as e:
            print(f"⚠️ 图片显示失败: {e}")
            print(f"📁 请手动查看: {filepath}")
            return False
    
    def print_ascii_art(self, img):
        """打印ASCII艺术版本"""
        try:
            # 转为灰度
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 二值化处理
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            
            # 调整大小
            height, width = binary.shape
            new_width = 40
            new_height = int(height * new_width / width * 0.5)  # 调整宽高比
            resized = cv2.resize(binary, (new_width, new_height))
            
            print("\n🎨 验证码预览:")
            print("┌" + "─" * new_width + "┐")
            
            for row in resized:
                line = "│"
                for pixel in row:
                    if pixel < 128:
                        line += "█"  # 黑色像素
                    else:
                        line += " "  # 白色像素
                line += "│"
                print(line)
            
            print("└" + "─" * new_width + "┘")
            
        except Exception as e:
            print(f"ASCII显示失败: {e}")
    
    def add_label(self, filename, label):
        """添加标注"""
        if len(label) != 4 or not label.isdigit():
            return False
        
        self.labels["labels"][filename] = {
            "label": label,
            "labeled_time": datetime.now().isoformat()
        }
        
        # 更新统计
        self.labels["labeled_count"] = len(self.labels["labels"])
        
        for digit in label:
            if digit in self.labels["statistics"]["digit_frequency"]:
                self.labels["statistics"]["digit_frequency"][digit] += 1
        
        return True
    
    def start_labeling(self, max_count=20):
        """开始标注"""
        unlabeled_files = self.get_unlabeled_files()
        
        if not unlabeled_files:
            print("✅ 没有需要标注的验证码")
            return
        
        total_files = len(unlabeled_files)
        labeled_count = len(self.labels.get("labels", {}))
        
        print(f"📊 标注状态:")
        print(f"总验证码数: {total_files + labeled_count}")
        print(f"已标注: {labeled_count}")
        print(f"待标注: {total_files}")
        print(f"本次标注: {min(max_count, total_files)} 个")
        
        print(f"\n🎯 开始标注...")
        print("操作说明:")
        print("- 输入4位数字进行标注")
        print("- 输入 's' 跳过")
        print("- 输入 'q' 退出")
        print("- 输入 'stats' 查看统计")
        
        labeled_this_session = 0
        skipped_this_session = 0
        
        try:
            for i, filename in enumerate(unlabeled_files[:max_count]):
                filepath = os.path.join(self.collection_dir, filename)
                
                print(f"\n{'='*60}")
                print(f"进度: {i+1}/{min(max_count, total_files)}")
                print(f"文件: {filename}")
                
                # 显示图片
                if self.display_image(filepath, filename):
                    print(f"📁 图片路径: {filepath}")
                
                while True:
                    user_input = input(f"\n请输入验证码 (4位数字): ").strip()
                    
                    if user_input.lower() == 'q':
                        print("👋 退出标注")
                        cv2.destroyAllWindows()
                        return
                    
                    elif user_input.lower() == 's':
                        print("⏭️ 跳过此验证码")
                        skipped_this_session += 1
                        break
                    
                    elif user_input.lower() == 'stats':
                        self.show_stats()
                        continue
                    
                    elif len(user_input) == 4 and user_input.isdigit():
                        if self.add_label(filename, user_input):
                            print(f"✅ 标注成功: {filename} -> {user_input}")
                            labeled_this_session += 1
                            break
                        else:
                            print("❌ 标注失败")
                    
                    else:
                        print("❌ 请输入4位数字")
                
                # 关闭当前图片窗口
                cv2.destroyAllWindows()
                
                # 每5个保存一次
                if (i + 1) % 5 == 0:
                    self.save_labels()
                    print(f"💾 已保存标注进度")
        
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断标注")
        
        finally:
            cv2.destroyAllWindows()
            self.save_labels()
            
            print(f"\n📊 本次标注结果:")
            print(f"已标注: {labeled_this_session} 个")
            print(f"跳过: {skipped_this_session} 个")
            print(f"总标注数: {len(self.labels.get('labels', {}))}")
    
    def show_stats(self):
        """显示统计信息"""
        labeled_count = len(self.labels.get("labels", {}))
        unlabeled_count = len(self.get_unlabeled_files())
        total_count = labeled_count + unlabeled_count
        
        print(f"\n📊 标注统计:")
        print(f"总数: {total_count}")
        print(f"已标注: {labeled_count}")
        print(f"未标注: {unlabeled_count}")
        print(f"进度: {labeled_count/total_count*100:.1f}%" if total_count > 0 else "进度: 0%")
        
        # 数字频率
        freq = self.labels.get("statistics", {}).get("digit_frequency", {})
        print(f"\n🔢 数字频率:")
        for digit in "0123456789":
            count = freq.get(digit, 0)
            if count > 0:
                print(f"  {digit}: {count} 次")

def main():
    """主函数"""
    print("🏷️ 简化验证码标注工具")
    print("=" * 50)
    
    labeler = SimpleCaptchaLabeler()
    
    # 显示当前状态
    labeler.show_stats()
    
    choice = input(f"\n选择标注数量:\n1. 标注 10 个\n2. 标注 20 个\n3. 标注 30 个\n4. 查看统计\n请选择 (1-4): ").strip()
    
    if choice == "1":
        labeler.start_labeling(10)
    elif choice == "2":
        labeler.start_labeling(20)
    elif choice == "3":
        labeler.start_labeling(30)
    elif choice == "4":
        labeler.show_stats()
    else:
        print("默认标注10个")
        labeler.start_labeling(10)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
