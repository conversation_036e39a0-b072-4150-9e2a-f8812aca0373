#!/usr/bin/env python3
"""
EIR页面结构分析工具
专门用于分析登录后的页面结构，找到打单功能入口
"""

import sys
import os
import time
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def analyze_eir_page_structure():
    """分析EIR页面结构"""
    print("🔍 EIR页面结构分析")
    print("=" * 50)
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("🔧 初始化EIR服务...")
        eir_service = EIRService()
        
        print("🔐 执行自动登录...")
        login_result = await eir_service.auto_login()
        
        if not login_result.get("success"):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        print("✅ 登录成功，开始分析页面结构...")
        
        # 获取浏览器实例
        automation = eir_service.automation
        driver = automation.driver
        
        if not driver:
            print("❌ 无法获取浏览器实例")
            return False
        
        # 等待页面完全加载
        time.sleep(5)
        
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📄 当前页面信息:")
        print(f"   URL: {current_url}")
        print(f"   标题: {page_title}")
        
        # 分析页面结构
        print(f"\n🔍 分析页面结构...")
        
        # 1. 查找所有链接
        print("1. 分析所有链接...")
        from selenium.webdriver.common.by import By
        
        all_links = driver.find_elements(By.TAG_NAME, "a")
        print(f"   找到 {len(all_links)} 个链接")
        
        relevant_links = []
        for link in all_links:
            try:
                text = link.text.strip()
                href = link.get_attribute("href")
                
                if text and len(text) < 100:  # 过滤太长的文本
                    # 查找与业务相关的链接
                    if any(keyword in text for keyword in ['业务', 'EIR', '打单', '缴费', '出口', '办理']):
                        relevant_links.append((text, href))
                        
            except:
                continue
        
        if relevant_links:
            print(f"   相关业务链接 ({len(relevant_links)} 个):")
            for i, (text, href) in enumerate(relevant_links[:15]):
                print(f"     {i+1}. {text} -> {href}")
        
        # 2. 查找菜单结构
        print(f"\n2. 分析菜单结构...")
        
        # 查找可能的菜单容器
        menu_selectors = [
            "//ul[contains(@class, 'menu')]",
            "//div[contains(@class, 'menu')]",
            "//table[contains(@class, 'menu')]",
            "//td[contains(@class, 'menu')]"
        ]
        
        for selector in menu_selectors:
            try:
                menu_elements = driver.find_elements(By.XPATH, selector)
                if menu_elements:
                    print(f"   找到菜单容器: {selector} ({len(menu_elements)} 个)")
                    
                    for i, menu in enumerate(menu_elements[:3]):
                        try:
                            menu_text = menu.text.strip()
                            if menu_text and len(menu_text) < 500:
                                print(f"     菜单 {i+1}: {menu_text[:200]}...")
                        except:
                            pass
            except:
                continue
        
        # 3. 查找表格结构
        print(f"\n3. 分析表格结构...")
        
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"   找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables[:5]):  # 分析前5个表格
            try:
                table_text = table.text.strip()
                if table_text and len(table_text) > 10:
                    # 查找包含业务关键词的表格
                    if any(keyword in table_text for keyword in ['业务', 'EIR', '打单', '缴费']):
                        print(f"     相关表格 {i+1}: {table_text[:200]}...")
            except:
                continue
        
        # 4. 查找特定关键词
        print(f"\n4. 查找特定关键词...")
        
        keywords = ['业务办理', 'EIR', '打单', '缴费', '出口', '进口', '办理']
        
        for keyword in keywords:
            try:
                elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                if elements:
                    print(f"   '{keyword}': 找到 {len(elements)} 个元素")
                    
                    for j, elem in enumerate(elements[:3]):
                        try:
                            text = elem.text.strip()
                            tag = elem.tag_name
                            if text and len(text) < 100:
                                print(f"     {j+1}. <{tag}> {text}")
                        except:
                            pass
            except:
                continue
        
        # 5. 查找iframe
        print(f"\n5. 查找iframe...")
        
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            print(f"   找到 {len(iframes)} 个iframe")
            
            for i, iframe in enumerate(iframes):
                try:
                    src = iframe.get_attribute("src")
                    name = iframe.get_attribute("name")
                    iframe_id = iframe.get_attribute("id")
                    
                    print(f"     iframe {i+1}: src={src}, name={name}, id={iframe_id}")
                    
                    # 尝试切换到iframe分析内容
                    if src or name or iframe_id:
                        try:
                            driver.switch_to.frame(iframe)
                            iframe_title = driver.title
                            iframe_links = driver.find_elements(By.TAG_NAME, "a")
                            
                            print(f"       iframe内容: 标题={iframe_title}, 链接数={len(iframe_links)}")
                            
                            # 查找iframe内的相关链接
                            for link in iframe_links[:5]:
                                try:
                                    link_text = link.text.strip()
                                    if link_text and any(keyword in link_text for keyword in ['业务', 'EIR', '打单']):
                                        print(f"         相关链接: {link_text}")
                                except:
                                    pass
                            
                            # 切换回主页面
                            driver.switch_to.default_content()
                            
                        except Exception as e:
                            print(f"       无法访问iframe内容: {e}")
                            driver.switch_to.default_content()
                            
                except:
                    continue
        
        # 6. 保存页面源码
        print(f"\n6. 保存页面源码...")
        
        try:
            page_source = driver.page_source
            timestamp = int(time.time())
            source_file = f"eir_page_source_{timestamp}.html"
            
            with open(source_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            print(f"   页面源码已保存: {source_file}")
            print(f"   源码长度: {len(page_source)} 字符")
            
        except Exception as e:
            print(f"   保存页面源码失败: {e}")
        
        # 7. 截图
        print(f"\n7. 保存页面截图...")
        
        try:
            screenshot_file = f"eir_page_screenshot_{int(time.time())}.png"
            driver.save_screenshot(screenshot_file)
            print(f"   页面截图已保存: {screenshot_file}")
            
        except Exception as e:
            print(f"   保存截图失败: {e}")
        
        # 询问是否保持浏览器打开
        print(f"\n🌐 分析完成")
        choice = input("是否保持浏览器打开以便手动查看？(y/n): ").strip().lower()
        
        if choice == 'y':
            print("浏览器将保持打开状态...")
            print("您可以手动查看页面结构，寻找【业务办理】->【出口EIR缴费并打单】")
            input("完成后按 Enter 键关闭浏览器...")
        
        # 关闭服务
        eir_service.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 页面分析异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 EIR页面结构分析工具")
    print("=" * 50)
    
    choice = input("""
选择分析模式:
1. 完整页面结构分析 (推荐)
2. 退出
请输入选择 (1-2): """).strip()
    
    if choice == "1":
        print("\n🚀 开始完整页面结构分析...")
        success = asyncio.run(analyze_eir_page_structure())
        
        if success:
            print("\n✅ 页面分析完成")
            print("📋 分析结果已保存为HTML文件和截图")
            print("💡 请查看输出信息，寻找打单功能的入口")
        else:
            print("\n❌ 页面分析失败")
    
    elif choice == "2":
        print("👋 退出分析工具")
        return
    
    else:
        print("无效选择，退出")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断分析")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
