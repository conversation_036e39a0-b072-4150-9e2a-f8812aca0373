#!/usr/bin/env python3
"""
专门测试机器学习验证码识别功能
不涉及网络连接
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_ml_model_performance():
    """测试机器学习模型性能"""
    print("🤖 测试机器学习验证码识别模型性能")
    print("=" * 60)
    
    try:
        from backend.app.services.captcha_service_ml import ml_captcha_service
        
        # 1. 检查模型状态
        print("1. 检查模型加载状态...")
        model_info = ml_captcha_service.get_model_info()
        
        if model_info.get("status") != "loaded":
            print("❌ 模型未正确加载")
            return False
        
        print("✅ 模型加载成功")
        print(f"   - 分位置模型: {model_info.get('models_count')} 个")
        print(f"   - 整体模型: {'是' if model_info.get('has_whole_model') else '否'}")
        
        training_info = model_info.get('training_info', {})
        if training_info:
            print(f"   - 训练样本: {training_info.get('sample_count')} 个")
            print(f"   - 训练时间: {training_info.get('training_time', 'N/A')}")
        
        # 2. 测试多个验证码
        print("\n2. 测试验证码识别性能...")
        collection_dir = "data/captcha_collection"
        
        if not os.path.exists(collection_dir):
            print("❌ 验证码目录不存在")
            return False
        
        test_files = [f for f in os.listdir(collection_dir) if f.endswith('.png')]
        if not test_files:
            print("❌ 没有找到验证码文件")
            return False
        
        # 测试前10个验证码
        test_count = min(10, len(test_files))
        print(f"测试 {test_count} 个验证码样本:")
        
        success_count = 0
        total_confidence = 0
        
        for i, filename in enumerate(test_files[:test_count]):
            filepath = os.path.join(collection_dir, filename)
            
            print(f"\n测试 {i+1}/{test_count}: {filename}")
            
            result = ml_captcha_service.recognize_captcha(filepath)
            
            if result.get("success"):
                prediction = result.get("best_result")
                confidence = result.get("confidence", 0)
                candidates = result.get("all_results", [])
                method = result.get("method", "unknown")
                
                print(f"   ✅ 识别成功: {prediction}")
                print(f"   📊 置信度: {confidence:.3f}")
                print(f"   🔧 方法: {method}")
                print(f"   📋 候选: {candidates}")
                
                success_count += 1
                total_confidence += confidence
            else:
                print(f"   ❌ 识别失败: {result.get('message', '未知错误')}")
        
        # 计算统计信息
        success_rate = success_count / test_count * 100
        avg_confidence = total_confidence / success_count if success_count > 0 else 0
        
        print(f"\n📊 性能统计:")
        print(f"成功识别: {success_count}/{test_count}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均置信度: {avg_confidence:.3f}")
        
        # 性能评估
        if success_rate >= 90:
            print("🎉 模型性能优秀！")
        elif success_rate >= 80:
            print("✅ 模型性能良好")
        elif success_rate >= 70:
            print("📈 模型性能尚可")
        else:
            print("⚠️ 模型性能需要改进")
        
        return success_rate >= 70
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_labeled_data():
    """使用标注数据测试模型准确率"""
    print("\n🎯 使用标注数据测试模型准确率")
    print("=" * 60)
    
    try:
        from backend.app.services.captcha_service_ml import ml_captcha_service
        
        # 加载标注数据
        labels_file = "data/captcha_labeled/labels.json"
        if not os.path.exists(labels_file):
            print("❌ 标注数据文件不存在")
            return False
        
        import json
        with open(labels_file, 'r', encoding='utf-8') as f:
            labels_data = json.load(f)
        
        labels = labels_data.get("labels", {})
        if not labels:
            print("❌ 没有标注数据")
            return False
        
        print(f"找到 {len(labels)} 个标注样本")
        
        # 测试前20个标注样本
        test_items = list(labels.items())[:20]
        print(f"测试前 {len(test_items)} 个样本:")
        
        correct_count = 0
        total_count = 0
        
        for filename, label_info in test_items:
            filepath = os.path.join("data/captcha_collection", filename)
            
            if not os.path.exists(filepath):
                continue
            
            true_label = label_info.get("label")
            if not true_label or len(true_label) != 4:
                continue
            
            print(f"\n测试: {filename}")
            print(f"真实标签: {true_label}")
            
            result = ml_captcha_service.recognize_captcha(filepath)
            
            if result.get("success"):
                prediction = result.get("best_result")
                confidence = result.get("confidence", 0)
                
                is_correct = prediction == true_label
                status = "✅ 正确" if is_correct else "❌ 错误"
                
                print(f"预测结果: {prediction}")
                print(f"置信度: {confidence:.3f}")
                print(f"结果: {status}")
                
                if is_correct:
                    correct_count += 1
                total_count += 1
            else:
                print(f"识别失败: {result.get('message')}")
                total_count += 1
        
        if total_count > 0:
            accuracy = correct_count / total_count * 100
            print(f"\n📊 准确率统计:")
            print(f"正确预测: {correct_count}/{total_count}")
            print(f"准确率: {accuracy:.1f}%")
            
            if accuracy >= 90:
                print("🎉 模型准确率优秀！")
            elif accuracy >= 80:
                print("✅ 模型准确率良好")
            elif accuracy >= 70:
                print("📈 模型准确率尚可")
            else:
                print("⚠️ 模型准确率需要改进")
            
            return accuracy >= 70
        else:
            print("❌ 没有有效的测试样本")
            return False
        
    except Exception as e:
        print(f"❌ 标注数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original_method():
    """与原始识别方法对比"""
    print("\n🔄 与原始识别方法对比")
    print("=" * 60)
    
    try:
        from backend.app.services.captcha_service_ml import ml_captcha_service
        from backend.app.services.captcha_service_digits import digit_captcha_service
        
        collection_dir = "data/captcha_collection"
        test_files = [f for f in os.listdir(collection_dir) if f.endswith('.png')]
        
        if not test_files:
            print("❌ 没有测试文件")
            return False
        
        # 测试前5个文件
        test_count = min(5, len(test_files))
        print(f"对比测试 {test_count} 个验证码:")
        
        ml_success = 0
        original_success = 0
        
        for i, filename in enumerate(test_files[:test_count]):
            filepath = os.path.join(collection_dir, filename)
            
            print(f"\n测试 {i+1}: {filename}")
            
            # ML方法
            ml_result = ml_captcha_service.recognize_captcha(filepath)
            if ml_result.get("success"):
                ml_prediction = ml_result.get("best_result")
                ml_confidence = ml_result.get("confidence", 0)
                print(f"   🤖 ML方法: {ml_prediction} (置信度: {ml_confidence:.3f})")
                ml_success += 1
            else:
                print(f"   🤖 ML方法: 失败")
            
            # 原始方法
            try:
                original_result = digit_captcha_service.recognize_captcha(filepath)
                if original_result.get("success"):
                    original_prediction = original_result.get("best_result")
                    print(f"   🔧 原始方法: {original_prediction}")
                    original_success += 1
                else:
                    print(f"   🔧 原始方法: 失败")
            except Exception as e:
                print(f"   🔧 原始方法: 异常 ({e})")
        
        print(f"\n📊 对比结果:")
        print(f"ML方法成功率: {ml_success}/{test_count} ({ml_success/test_count*100:.1f}%)")
        print(f"原始方法成功率: {original_success}/{test_count} ({original_success/test_count*100:.1f}%)")
        
        if ml_success > original_success:
            print("🎉 ML方法表现更好！")
        elif ml_success == original_success:
            print("📊 两种方法表现相当")
        else:
            print("🔧 原始方法表现更好")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 机器学习验证码识别专项测试")
    print("=" * 60)
    
    choice = input("""
选择测试模式:
1. 测试模型性能 (10个样本)
2. 使用标注数据测试准确率
3. 与原始方法对比
4. 完整测试 (推荐)
请输入选择 (1-4): """).strip()
    
    if choice == "1":
        test_ml_model_performance()
    elif choice == "2":
        test_with_labeled_data()
    elif choice == "3":
        compare_with_original_method()
    elif choice == "4":
        print("🚀 运行完整测试...")
        
        tests = [
            ("模型性能测试", test_ml_model_performance),
            ("标注数据准确率测试", test_with_labeled_data),
            ("方法对比测试", compare_with_original_method)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                results.append((test_name, False))
        
        # 显示总结
        print(f"\n{'='*60}")
        print("🎯 测试总结")
        print("=" * 60)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        overall_success = passed / len(results) * 100
        print(f"\n总体通过率: {passed}/{len(results)} ({overall_success:.1f}%)")
        
        if overall_success >= 80:
            print("🎉 机器学习模型测试成功！")
        else:
            print("⚠️ 机器学习模型需要优化")
    
    else:
        print("无效选择，运行模型性能测试")
        test_ml_model_performance()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
