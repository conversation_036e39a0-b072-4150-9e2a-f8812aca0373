<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EIR登录测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .captcha-img {
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            max-height: 50px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            display: none;
            text-align: center;
            color: #666;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>EIR登录测试</h1>

        <div id="status-display"></div>

        <div class="form-group">
            <button id="get-captcha-btn" onclick="getCaptcha()">获取验证码</button>
            <button id="auto-login-btn" onclick="autoLogin()">自动登录</button>
            <button id="check-status-btn" onclick="checkStatus()">检查登录状态</button>
        </div>

        <div id="captcha-section" class="hidden">
            <div class="form-group">
                <label>验证码图片：</label>
                <div class="captcha-container">
                    <img id="captcha-img" class="captcha-img" onclick="refreshCaptcha()" title="点击刷新验证码">
                    <button onclick="refreshCaptcha()">刷新验证码</button>
                </div>
            </div>

            <div class="form-group">
                <label for="captcha-input">请输入验证码：</label>
                <input type="text" id="captcha-input" placeholder="请输入验证码" maxlength="4">
            </div>

            <div class="form-group">
                <button id="login-btn" onclick="login()">登录</button>
            </div>
        </div>

        <div id="loading" class="loading">
            <p>处理中，请稍候...</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showLoading(show = true) {
            const loadingDiv = document.getElementById('loading');
            loadingDiv.style.display = show ? 'block' : 'none';
        }

        function toggleButtons(disabled = false) {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = disabled);
        }

        async function getCaptcha() {
            showLoading(true);
            toggleButtons(true);

            try {
                const response = await fetch(`${API_BASE}/eir/captcha`);
                const result = await response.json();

                if (result.success) {
                    document.getElementById('captcha-img').src = `http://localhost:8001${result.captcha_url}`;
                    document.getElementById('captcha-section').classList.remove('hidden');
                    showStatus('验证码获取成功，请输入验证码', 'success');
                } else {
                    showStatus(`验证码获取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                toggleButtons(false);
            }
        }

        async function refreshCaptcha() {
            showLoading(true);
            toggleButtons(true);

            try {
                const response = await fetch(`${API_BASE}/eir/captcha/refresh`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    document.getElementById('captcha-img').src = `http://localhost:8001${result.captcha_url}`;
                    document.getElementById('captcha-input').value = '';
                    showStatus('验证码已刷新', 'success');
                } else {
                    showStatus(`验证码刷新失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                toggleButtons(false);
            }
        }

        async function login() {
            const captchaCode = document.getElementById('captcha-input').value.trim();

            if (!captchaCode) {
                showStatus('请输入验证码', 'error');
                return;
            }

            showLoading(true);
            toggleButtons(true);

            try {
                const response = await fetch(`${API_BASE}/eir/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        captcha_code: captchaCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus('登录成功！Cookies已保存', 'success');
                    document.getElementById('captcha-section').classList.add('hidden');
                } else {
                    showStatus(`登录失败: ${result.message}`, 'error');
                    if (result.message.includes('验证码')) {
                        // 验证码错误，自动刷新验证码
                        setTimeout(refreshCaptcha, 1000);
                    }
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                toggleButtons(false);
            }
        }

        async function autoLogin() {
            showLoading(true);
            toggleButtons(true);
            showStatus('正在执行自动登录（包括验证码识别）...', 'info');

            try {
                const response = await fetch(`${API_BASE}/eir/auto-login`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showStatus(`自动登录成功！${result.message}`, 'success');
                    document.getElementById('captcha-section').classList.add('hidden');
                } else {
                    showStatus(`自动登录失败: ${result.message}`, 'error');
                    if (result.message.includes('验证码自动识别失败')) {
                        showStatus('自动识别失败，请手动获取验证码进行登录', 'info');
                        setTimeout(getCaptcha, 1000);
                    }
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                toggleButtons(false);
            }
        }

        async function checkStatus() {
            showLoading(true);
            toggleButtons(true);

            try {
                const response = await fetch(`${API_BASE}/eir/status`);
                const result = await response.json();

                if (result.success) {
                    const status = result.is_logged_in ? '已登录' : '未登录';
                    const type = result.is_logged_in ? 'success' : 'info';
                    showStatus(`当前状态: ${status}`, type);
                } else {
                    showStatus(`状态检查失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                toggleButtons(false);
            }
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkStatus();
        };

        // 回车键登录
        document.getElementById('captcha-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });
    </script>
</body>
</html>
