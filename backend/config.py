import os
from typing import Dict, Any

class Config:
    # 数据库配置
    DATABASE_PATH = "logistics.db"

    # EIR配置
    EIR_BASE_URL = "https://eir.cmclink.com"
    EIR_USERNAME = "Boshanwl"
    EIR_PASSWORD = "BOS,./"

    # 约柜配置
    DPAY_BASE_URL = "https://dpay.cmclink.com"
    DPAY_USERNAME = "Boshanwl"
    DPAY_PASSWORD = "BOS,./"

    # 文件存储配置
    DATA_DIR = "data"
    CAPTCHA_DIR = os.path.join(DATA_DIR, "captcha")
    COOKIES_DIR = os.path.join(DATA_DIR, "cookies")
    PDF_DIR = os.path.join(DATA_DIR, "pdfs")

    # 浏览器配置
    BROWSER_TIMEOUT = 30
    PAGE_LOAD_TIMEOUT = 10

    # 代理配置（由智能代理管理器自动处理）
    PROXY_HOST = "127.0.0.1"
    PROXY_PORT = "7897"

    # 验证码识别配置
    CAPTCHA_CONFIDENCE_THRESHOLD = 0.3
    CAPTCHA_MAX_RETRY = 3

    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.DATA_DIR,
            cls.CAPTCHA_DIR,
            cls.COOKIES_DIR,
            cls.PDF_DIR
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
