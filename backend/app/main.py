from fastapi import FastAP<PERSON>, HTTPException, File, UploadFile
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import os
import uvicorn

from .services.eir_service import EIRService

app = FastAPI(title="物流运输管理系统", description="EIR打单自动化系统", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
os.makedirs("data/captcha", exist_ok=True)
app.mount("/captcha", StaticFiles(directory="data/captcha"), name="captcha")

# 全局服务实例
eir_service = EIRService()

class LoginRequest(BaseModel):
    captcha_code: str

@app.get("/")
async def root():
    return {"message": "物流运输管理系统 API", "version": "1.0.0"}

@app.get("/api/eir/captcha")
async def get_captcha():
    """获取EIR登录验证码"""
    result = await eir_service.get_login_captcha()
    if result["success"]:
        # 返回验证码图片的URL
        captcha_filename = os.path.basename(result["captcha_path"])
        result["captcha_url"] = f"/captcha/{captcha_filename}"
    return result

@app.post("/api/eir/captcha/refresh")
async def refresh_captcha():
    """刷新EIR验证码"""
    result = await eir_service.refresh_captcha()
    if result["success"]:
        # 返回验证码图片的URL
        captcha_filename = os.path.basename(result["captcha_path"])
        result["captcha_url"] = f"/captcha/{captcha_filename}"
    return result

@app.post("/api/eir/login")
async def login(request: LoginRequest):
    """EIR登录"""
    result = await eir_service.login(request.captcha_code)
    return result

@app.get("/api/eir/status")
async def check_status():
    """检查EIR登录状态"""
    result = await eir_service.check_login_status()
    return result

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    eir_service.close()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
