import os
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from typing import Dict, Any, Optional, Tuple
import base64
from PIL import Image
import io

from ..models.database import DatabaseManager
from ..services.captcha_service_digits import digit_captcha_service
from ...config import Config

class EIRAutomation:
    def __init__(self):
        self.driver = None
        self.db = DatabaseManager()
        self.base_url = "https://eir.cmclink.com"
        self.username = "Boshanwl"
        self.password = Config.EIR_PASSWORD
        self.captcha_dir = Config.CAPTCHA_DIR
        self.cookies_dir = Config.COOKIES_DIR

        # 创建必要的目录
        os.makedirs(self.captcha_dir, exist_ok=True)
        os.makedirs(self.cookies_dir, exist_ok=True)

    def init_driver(self):
        """初始化Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # EIR是国内网站，不需要代理
            self.db.log_operation("init_driver", "EIR", "EIR网站无需代理，直连访问")

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置超时时间
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)

            self.db.log_operation("init_driver", "EIR", "浏览器驱动初始化成功")
            return True
        except Exception as e:
            self.db.log_operation("init_driver", "EIR", f"浏览器驱动初始化失败: {str(e)}", "error", str(e))
            return False

    def load_cookies(self) -> bool:
        """加载保存的cookies"""
        try:
            cookies = self.db.get_cookies("EIR", self.username)
            if cookies:
                # 先访问网站以设置域名
                self.driver.get(self.base_url)
                time.sleep(2)

                # 添加cookies
                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except Exception as e:
                        print(f"添加cookie失败: {e}")

                # 刷新页面以应用cookies
                self.driver.refresh()
                time.sleep(3)

                # 检查是否已登录
                if self.check_login_status():
                    self.db.log_operation("load_cookies", "EIR", "Cookies加载成功，已登录")
                    return True
                else:
                    self.db.log_operation("load_cookies", "EIR", "Cookies已过期", "warning")
                    return False
            return False
        except Exception as e:
            self.db.log_operation("load_cookies", "EIR", f"加载cookies失败: {str(e)}", "error", str(e))
            return False

    def save_cookies(self):
        """保存当前的cookies"""
        try:
            cookies = self.driver.get_cookies()
            self.db.save_cookies("EIR", self.username, cookies)
            self.db.log_operation("save_cookies", "EIR", "Cookies保存成功")
        except Exception as e:
            self.db.log_operation("save_cookies", "EIR", f"保存cookies失败: {str(e)}", "error", str(e))

    def check_login_status(self) -> bool:
        """检查是否已登录"""
        try:
            # 检查页面是否包含登录后的元素
            # 这里需要根据实际登录后的页面特征来判断
            WebDriverWait(self.driver, 5).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            # 如果页面包含登录表单，说明未登录
            login_form = self.driver.find_elements(By.NAME, "txtUserName")
            if login_form:
                return False

            # 检查是否有用户信息或主菜单
            user_info = self.driver.find_elements(By.XPATH, "//a[contains(text(), '退出')]")
            if user_info:
                return True

            return False
        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False

    def download_captcha(self) -> Optional[str]:
        """下载验证码图片"""
        try:
            # 查找验证码图片元素
            captcha_img = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//img[contains(@src, 'ValidateCode')]"))
            )

            # 获取验证码图片的src
            captcha_src = captcha_img.get_attribute("src")

            # 如果是base64编码的图片
            if captcha_src.startswith("data:image"):
                # 解析base64数据
                header, data = captcha_src.split(',', 1)
                image_data = base64.b64decode(data)
            else:
                # 如果是URL，则下载图片
                response = requests.get(captcha_src, cookies={cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()})
                image_data = response.content

            # 保存验证码图片
            timestamp = int(time.time())
            captcha_path = os.path.join(self.captcha_dir, f"captcha_{timestamp}.png")

            with open(captcha_path, "wb") as f:
                f.write(image_data)

            self.db.log_operation("download_captcha", "EIR", f"验证码下载成功: {captcha_path}")
            return captcha_path

        except Exception as e:
            self.db.log_operation("download_captcha", "EIR", f"下载验证码失败: {str(e)}", "error", str(e))
            return None

    def auto_recognize_captcha(self, captcha_path: str) -> Optional[str]:
        """自动识别验证码"""
        try:
            if not captcha_path or not os.path.exists(captcha_path):
                return None

            self.db.log_operation("auto_recognize_captcha", "EIR", f"开始自动识别验证码: {captcha_path}")

            # 使用数字验证码识别服务
            result = digit_captcha_service.recognize_captcha(captcha_path)

            if result['success']:
                best_result = result['best_result']
                confidence = result['confidence']
                all_results = result['all_results']

                self.db.log_operation(
                    "auto_recognize_captcha",
                    "EIR",
                    f"验证码识别成功: {best_result} (置信度: {confidence}, 所有结果: {all_results})"
                )

                # 只返回置信度较高的结果
                if confidence >= 0.3:  # 至少有30%的置信度
                    return best_result
                else:
                    self.db.log_operation(
                        "auto_recognize_captcha",
                        "EIR",
                        f"验证码识别置信度较低: {confidence}",
                        "warning"
                    )
                    return None
            else:
                self.db.log_operation(
                    "auto_recognize_captcha",
                    "EIR",
                    f"验证码识别失败: {result['message']}",
                    "error"
                )
                return None

        except Exception as e:
            self.db.log_operation(
                "auto_recognize_captcha",
                "EIR",
                f"自动识别验证码异常: {str(e)}",
                "error",
                str(e)
            )
            return None

    def auto_login_with_retry(self) -> Tuple[bool, str]:
        """智能重试自动登录（包括多个验证码候选）"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                self.db.log_operation("auto_login_retry", "EIR", f"开始第 {attempt + 1} 次登录尝试")

                if not self.driver:
                    if not self.init_driver():
                        return False, "浏览器驱动初始化失败"

                # 访问登录页面
                self.driver.get(self.base_url)
                time.sleep(3)

                # 先尝试加载已保存的cookies
                if attempt == 0 and self.load_cookies():
                    return True, "使用保存的cookies登录成功"

                # 等待页面加载完成
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.NAME, "txtUserName"))
                )

                # 填写用户名
                username_input = self.driver.find_element(By.NAME, "txtUserName")
                username_input.clear()
                username_input.send_keys(self.username)

                # 填写密码
                password_input = self.driver.find_element(By.NAME, "txtPassword")
                password_input.clear()
                password_input.send_keys(self.password)

                # 下载验证码
                captcha_path = self.download_captcha()
                if not captcha_path:
                    self.db.log_operation("auto_login_retry", "EIR", f"第 {attempt + 1} 次尝试: 验证码下载失败", "warning")
                    continue

                # 自动识别验证码，获取多个候选
                recognition_result = digit_captcha_service.recognize_captcha(captcha_path)
                if not recognition_result['success']:
                    self.db.log_operation("auto_login_retry", "EIR", f"第 {attempt + 1} 次尝试: 验证码识别失败", "warning")
                    continue

                candidates = recognition_result['all_results']
                if not candidates:
                    candidates = [recognition_result['best_result']]

                # 尝试每个候选验证码
                for i, captcha_code in enumerate(candidates[:3]):  # 最多尝试3个候选
                    try:
                        self.db.log_operation("auto_login_retry", "EIR", f"第 {attempt + 1} 次尝试，候选 {i + 1}: {captcha_code}")

                        # 填写验证码
                        captcha_input = self.driver.find_element(By.NAME, "txtValidateCode")
                        captcha_input.clear()
                        captcha_input.send_keys(captcha_code)

                        # 点击登录按钮
                        login_button = self.driver.find_element(By.NAME, "btnLogin")
                        login_button.click()

                        # 等待登录结果
                        time.sleep(5)

                        # 检查是否登录成功
                        if self.check_login_status():
                            # 保存cookies
                            self.save_cookies()
                            success_msg = f"自动登录成功（第 {attempt + 1} 次尝试，候选 {i + 1}: {captcha_code}）"
                            self.db.log_operation("auto_login_retry", "EIR", success_msg)
                            return True, success_msg
                        else:
                            # 检查错误信息
                            error_elements = self.driver.find_elements(By.XPATH, "//span[@id='lblMessage']")
                            if error_elements:
                                error_msg = error_elements[0].text
                                self.db.log_operation("auto_login_retry", "EIR", f"候选 {i + 1} 失败: {error_msg}", "warning")
                                if "验证码" in error_msg:
                                    # 验证码错误，尝试下一个候选
                                    continue
                                else:
                                    # 其他错误，停止尝试
                                    return False, f"登录失败: {error_msg}"

                            # 刷新页面准备下一次尝试
                            if i < len(candidates) - 1:
                                self.driver.refresh()
                                time.sleep(3)
                                # 重新填写用户名和密码
                                username_input = self.driver.find_element(By.NAME, "txtUserName")
                                username_input.clear()
                                username_input.send_keys(self.username)
                                password_input = self.driver.find_element(By.NAME, "txtPassword")
                                password_input.clear()
                                password_input.send_keys(self.password)

                    except Exception as e:
                        self.db.log_operation("auto_login_retry", "EIR", f"候选 {i + 1} 尝试异常: {str(e)}", "warning")
                        continue

                # 所有候选都失败，准备下一轮尝试
                if attempt < max_retries - 1:
                    self.db.log_operation("auto_login_retry", "EIR", f"第 {attempt + 1} 次尝试失败，准备下一轮", "warning")
                    time.sleep(2)

            except TimeoutException:
                error_msg = f"第 {attempt + 1} 次尝试: 页面加载超时"
                self.db.log_operation("auto_login_retry", "EIR", error_msg, "error")
                if attempt == max_retries - 1:
                    return False, error_msg
            except Exception as e:
                error_msg = f"第 {attempt + 1} 次尝试异常: {str(e)}"
                self.db.log_operation("auto_login_retry", "EIR", error_msg, "error")
                if attempt == max_retries - 1:
                    return False, error_msg

        return False, f"经过 {max_retries} 次尝试，自动登录失败"

    def auto_login(self) -> Tuple[bool, str]:
        """自动登录（包括自动验证码识别）"""
        return self.auto_login_with_retry()

    def login(self, captcha_code: str) -> Tuple[bool, str]:
        """执行登录操作"""
        try:
            if not self.driver:
                if not self.init_driver():
                    return False, "浏览器驱动初始化失败"

            # 访问登录页面
            self.driver.get(self.base_url)
            time.sleep(3)

            # 先尝试加载已保存的cookies
            if self.load_cookies():
                return True, "使用保存的cookies登录成功"

            # 等待页面加载完成
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "txtUserName"))
            )

            # 填写用户名
            username_input = self.driver.find_element(By.NAME, "txtUserName")
            username_input.clear()
            username_input.send_keys(self.username)

            # 填写密码
            password_input = self.driver.find_element(By.NAME, "txtPassword")
            password_input.clear()
            password_input.send_keys(self.password)

            # 填写验证码
            captcha_input = self.driver.find_element(By.NAME, "txtValidateCode")
            captcha_input.clear()
            captcha_input.send_keys(captcha_code)

            # 点击登录按钮
            login_button = self.driver.find_element(By.NAME, "btnLogin")
            login_button.click()

            # 等待登录结果
            time.sleep(5)

            # 检查是否登录成功
            if self.check_login_status():
                # 保存cookies
                self.save_cookies()
                self.db.log_operation("login", "EIR", "登录成功")
                return True, "登录成功"
            else:
                # 检查是否有错误信息
                error_elements = self.driver.find_elements(By.XPATH, "//span[@id='lblMessage']")
                if error_elements:
                    error_msg = error_elements[0].text
                    self.db.log_operation("login", "EIR", f"登录失败: {error_msg}", "error", error_msg)
                    return False, f"登录失败: {error_msg}"
                else:
                    self.db.log_operation("login", "EIR", "登录失败: 未知错误", "error")
                    return False, "登录失败: 未知错误"

        except TimeoutException:
            error_msg = "页面加载超时"
            self.db.log_operation("login", "EIR", error_msg, "error", error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"登录过程中发生错误: {str(e)}"
            self.db.log_operation("login", "EIR", error_msg, "error", str(e))
            return False, error_msg

    def get_captcha_for_login(self) -> Optional[str]:
        """获取登录验证码图片路径"""
        try:
            if not self.driver:
                if not self.init_driver():
                    return None

            # 访问登录页面
            self.driver.get(self.base_url)
            time.sleep(3)

            # 下载验证码
            return self.download_captcha()

        except Exception as e:
            self.db.log_operation("get_captcha", "EIR", f"获取验证码失败: {str(e)}", "error", str(e))
            return None

    def refresh_captcha(self) -> Optional[str]:
        """刷新验证码"""
        try:
            # 点击验证码图片刷新
            captcha_img = self.driver.find_element(By.XPATH, "//img[contains(@src, 'ValidateCode')]")
            captcha_img.click()
            time.sleep(2)

            # 下载新的验证码
            return self.download_captcha()

        except Exception as e:
            self.db.log_operation("refresh_captcha", "EIR", f"刷新验证码失败: {str(e)}", "error", str(e))
            return None

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.db.log_operation("close_driver", "EIR", "浏览器已关闭")

    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        self.close()
