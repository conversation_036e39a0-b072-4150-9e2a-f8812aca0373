"""
简单验证码收集器
使用requests直接获取验证码，不依赖浏览器
"""

import os
import time
import json
import requests
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
import cv2
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleCaptchaCollector:
    def __init__(self):
        self.base_url = "https://eir.cmclink.com"
        self.collection_dir = "data/captcha_collection"
        self.metadata_file = "data/captcha_collection/metadata.json"

        # 创建收集目录
        os.makedirs(self.collection_dir, exist_ok=True)

        # 初始化元数据
        self.metadata = self.load_metadata()

        # 会话管理
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

    def load_metadata(self) -> Dict[str, Any]:
        """加载元数据"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass

        return {
            "total_collected": 0,
            "collection_sessions": [],
            "captcha_files": [],
            "statistics": {
                "success_count": 0,
                "failed_count": 0,
                "duplicate_count": 0
            }
        }

    def save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")

    def get_login_page(self) -> bool:
        """获取登录页面，建立会话"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                logger.info("成功访问登录页面")
                return True
            else:
                logger.error(f"访问登录页面失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"访问登录页面异常: {e}")
            return False

    def download_captcha(self, session_id: str, index: int) -> Optional[Dict[str, Any]]:
        """下载单个验证码"""
        try:
            # 构造验证码URL（根据实际网站结构）
            timestamp = int(time.time() * 1000)
            captcha_url = f"{self.base_url}/freeaccess/validatecode.aspx?t={timestamp}"

            # 下载验证码
            response = self.session.get(captcha_url, timeout=10)

            if response.status_code == 200:
                # 保存验证码文件
                filename = f"captcha_{session_id}_{index:04d}_{timestamp}.png"
                filepath = os.path.join(self.collection_dir, filename)

                with open(filepath, 'wb') as f:
                    f.write(response.content)

                # 验证图片是否有效
                if self.validate_captcha_image(filepath):
                    # 分析图片特征
                    features = self.analyze_captcha_features(filepath)

                    captcha_info = {
                        "filename": filename,
                        "filepath": filepath,
                        "session_id": session_id,
                        "index": index,
                        "timestamp": timestamp,
                        "download_time": datetime.now().isoformat(),
                        "file_size": os.path.getsize(filepath),
                        "features": features,
                        "labeled": False,
                        "label": None
                    }

                    logger.info(f"✅ 第 {index} 个验证码下载成功: {filename}")
                    return captcha_info
                else:
                    # 删除无效图片
                    if os.path.exists(filepath):
                        os.remove(filepath)
                    logger.warning(f"第 {index} 个验证码: 图片无效，已删除")
                    return None
            else:
                logger.warning(f"第 {index} 个验证码: 下载失败，状态码 {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"第 {index} 个验证码下载异常: {e}")
            return None

    def validate_captcha_image(self, filepath: str) -> bool:
        """验证验证码图片是否有效"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(filepath)
            if file_size < 500:  # 太小的文件可能无效
                return False

            # 使用OpenCV读取图片
            img = cv2.imread(filepath)
            if img is None:
                return False

            # 检查图片尺寸
            height, width = img.shape[:2]
            if height < 15 or width < 30:  # 尺寸太小
                return False

            # 检查是否为空白图片
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            if np.std(gray) < 10:  # 标准差太小，可能是空白图片
                return False

            return True

        except Exception as e:
            logger.error(f"验证图片失败: {e}")
            return False

    def analyze_captcha_features(self, filepath: str) -> Dict[str, Any]:
        """分析验证码图片特征"""
        try:
            img = cv2.imread(filepath)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            height, width = gray.shape

            # 基本特征
            features = {
                "width": width,
                "height": height,
                "aspect_ratio": width / height,
                "file_size": os.path.getsize(filepath)
            }

            # 像素统计
            features["mean_intensity"] = float(np.mean(gray))
            features["std_intensity"] = float(np.std(gray))
            features["min_intensity"] = int(np.min(gray))
            features["max_intensity"] = int(np.max(gray))

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            features["edge_density"] = float(np.sum(edges) / (width * height))

            # 二值化分析
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            features["black_pixel_ratio"] = float(np.sum(binary == 0) / (width * height))

            return features

        except Exception as e:
            logger.error(f"分析图片特征失败: {e}")
            return {}

    def collect_captchas(self, target_count: int = 100, batch_size: int = 10) -> Dict[str, Any]:
        """批量收集验证码"""
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        session_info = {
            "session_id": session_id,
            "start_time": datetime.now().isoformat(),
            "target_count": target_count,
            "batch_size": batch_size,
            "collected_count": 0,
            "success_count": 0,
            "failed_count": 0,
            "status": "running"
        }

        logger.info(f"开始收集验证码，目标数量: {target_count}")

        # 首先访问登录页面建立会话
        if not self.get_login_page():
            session_info["status"] = "failed"
            session_info["error"] = "无法访问登录页面"
            return session_info

        try:
            collected_files = []

            for i in range(target_count):
                try:
                    # 每批次后短暂休息
                    if i > 0 and i % batch_size == 0:
                        logger.info(f"已收集 {i} 个验证码，休息 3 秒...")
                        time.sleep(3)
                        # 重新访问登录页面刷新会话
                        self.get_login_page()

                    captcha_info = self.download_captcha(session_id, i + 1)

                    if captcha_info:
                        collected_files.append(captcha_info)
                        session_info["success_count"] += 1
                        self.metadata["statistics"]["success_count"] += 1
                    else:
                        session_info["failed_count"] += 1
                        self.metadata["statistics"]["failed_count"] += 1

                    session_info["collected_count"] = i + 1

                    # 每10个验证码保存一次元数据
                    if (i + 1) % 10 == 0:
                        self.metadata["captcha_files"].extend(collected_files[-10:])
                        self.metadata["total_collected"] = len(self.metadata["captcha_files"])
                        self.save_metadata()
                        logger.info(f"进度: {i + 1}/{target_count} ({(i + 1)/target_count*100:.1f}%)")

                    # 随机延迟，避免被检测
                    time.sleep(np.random.uniform(0.5, 2.0))

                except KeyboardInterrupt:
                    logger.info("用户中断收集")
                    break
                except Exception as e:
                    logger.error(f"收集第 {i + 1} 个验证码时发生错误: {e}")
                    session_info["failed_count"] += 1
                    continue

            # 更新会话信息
            session_info["end_time"] = datetime.now().isoformat()
            session_info["status"] = "completed"
            session_info["collected_files"] = collected_files

            # 更新元数据
            self.metadata["captcha_files"].extend(collected_files)
            self.metadata["total_collected"] = len(self.metadata["captcha_files"])
            self.metadata["collection_sessions"].append(session_info)
            self.save_metadata()

            logger.info(f"验证码收集完成！成功: {session_info['success_count']}, 失败: {session_info['failed_count']}")

        except Exception as e:
            session_info["status"] = "error"
            session_info["error"] = str(e)
            logger.error(f"收集过程发生错误: {e}")

        return session_info

    def get_collection_statistics(self) -> Dict[str, Any]:
        """获取收集统计信息"""
        stats = {
            "total_collected": self.metadata["total_collected"],
            "statistics": self.metadata["statistics"],
            "sessions_count": len(self.metadata["collection_sessions"]),
            "collection_dir": self.collection_dir
        }

        # 分析文件大小分布
        if self.metadata["captcha_files"]:
            file_sizes = [f.get("file_size", 0) for f in self.metadata["captcha_files"]]
            stats["file_size_stats"] = {
                "min": min(file_sizes),
                "max": max(file_sizes),
                "mean": np.mean(file_sizes),
                "std": np.std(file_sizes)
            }

        # 分析图片特征分布
        if self.metadata["captcha_files"]:
            widths = [f.get("features", {}).get("width", 0) for f in self.metadata["captcha_files"]]
            heights = [f.get("features", {}).get("height", 0) for f in self.metadata["captcha_files"]]

            if widths and heights:
                stats["image_stats"] = {
                    "width_range": [min(widths), max(widths)],
                    "height_range": [min(heights), max(heights)],
                    "common_size": f"{max(set(widths), key=widths.count)}x{max(set(heights), key=heights.count)}"
                }

        return stats

# 全局实例
simple_captcha_collector = SimpleCaptchaCollector()
