"""
验证码标注工具
用于手动标注收集到的验证码，生成训练数据
"""

import os
import json
import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CaptchaLabeler:
    def __init__(self):
        self.collection_dir = "data/captcha_collection"
        self.labeled_dir = "data/captcha_labeled"
        self.metadata_file = "data/captcha_collection/metadata.json"
        self.labels_file = "data/captcha_labeled/labels.json"

        # 创建标注目录
        os.makedirs(self.labeled_dir, exist_ok=True)

        # 加载数据
        self.metadata = self.load_metadata()
        self.labels = self.load_labels()

    def load_metadata(self) -> Dict[str, Any]:
        """加载验证码元数据"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载元数据失败: {e}")
        return {"captcha_files": []}

    def load_labels(self) -> Dict[str, Any]:
        """加载标注数据"""
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载标注数据失败: {e}")

        return {
            "labeled_count": 0,
            "labels": {},
            "statistics": {
                "digit_frequency": {str(i): 0 for i in range(10)},
                "length_distribution": {},
                "labeling_sessions": []
            }
        }

    def save_labels(self):
        """保存标注数据"""
        try:
            with open(self.labels_file, 'w', encoding='utf-8') as f:
                json.dump(self.labels, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存标注数据失败: {e}")

    def get_unlabeled_captchas(self) -> List[Dict[str, Any]]:
        """获取未标注的验证码列表"""
        unlabeled = []

        for captcha_info in self.metadata.get("captcha_files", []):
            filename = captcha_info.get("filename", "")
            if filename not in self.labels.get("labels", {}):
                # 检查文件是否存在
                filepath = captcha_info.get("filepath", "")
                if os.path.exists(filepath):
                    unlabeled.append(captcha_info)

        # 如果没有元数据，直接扫描文件夹
        if not unlabeled:
            collection_dir = "data/captcha_collection"
            if os.path.exists(collection_dir):
                for filename in os.listdir(collection_dir):
                    if filename.endswith('.png') and filename not in self.labels.get("labels", {}):
                        filepath = os.path.join(collection_dir, filename)
                        if os.path.exists(filepath):
                            # 创建临时信息
                            captcha_info = {
                                "filename": filename,
                                "filepath": filepath,
                                "features": self.analyze_image_features(filepath)
                            }
                            unlabeled.append(captcha_info)

        return unlabeled

    def analyze_image_features(self, filepath: str) -> Dict[str, Any]:
        """分析图片特征"""
        try:
            import cv2
            import numpy as np

            img = cv2.imread(filepath)
            if img is None:
                return {}

            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape

            return {
                "width": width,
                "height": height,
                "file_size": os.path.getsize(filepath),
                "mean_intensity": float(np.mean(gray)),
                "edge_density": 0.0,  # 简化版本
                "black_pixel_ratio": 0.0  # 简化版本
            }
        except:
            return {}

    def display_captcha_image(self, filepath: str, filename: str):
        """优化的图片显示方法"""
        try:
            import cv2
            import numpy as np

            img = cv2.imread(filepath)
            if img is None:
                print("\u26a0\ufe0f  无法读取图片")
                return

            # 放大图片便于查看
            scale = 8  # 更大的放大倍数
            height, width = img.shape[:2]
            enlarged = cv2.resize(img, (width * scale, height * scale), interpolation=cv2.INTER_NEAREST)

            # 设置窗口属性
            window_name = f"验证码 - {filename}"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(window_name, width * scale, height * scale)

            # 显示图片
            cv2.imshow(window_name, enlarged)

            # 设置窗口位置（尽量显示在屏幕中央）
            cv2.moveWindow(window_name, 100, 100)

            # 非阻塞显示
            cv2.waitKey(1)

            # 打印ASCII艺术版本（备用）
            self.print_ascii_captcha(img)

        except Exception as e:
            print(f"\u26a0\ufe0f  图片显示失败: {e}")
            # 如果显示失败，提供替代方案
            print(f"📁 请手动查看图片: {filepath}")

    def print_ascii_captcha(self, img):
        """打印ASCII版本的验证码（备用方案）"""
        try:
            import cv2
            import numpy as np

            # 转为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 调整尺寸以适合终端显示
            height, width = gray.shape
            new_width = 50
            new_height = int(height * new_width / width)
            resized = cv2.resize(gray, (new_width, new_height))

            # 转为ASCII
            ascii_chars = "@%#*+=-:. "
            ascii_img = ""

            for row in resized:
                for pixel in row:
                    ascii_img += ascii_chars[pixel // 25]
                ascii_img += "\n"

            print("\n🎨 ASCII版本:")
            print("-" * 52)
            print(ascii_img)
            print("-" * 52)

        except:
            pass

    def display_captcha_info(self, captcha_info: Dict[str, Any]) -> str:
        """显示验证码信息"""
        filename = captcha_info.get("filename", "")
        features = captcha_info.get("features", {})

        info = f"""
验证码信息:
文件名: {filename}
尺寸: {features.get('width', 0)}x{features.get('height', 0)}
文件大小: {features.get('file_size', 0)} bytes
平均亮度: {features.get('mean_intensity', 0):.1f}
边缘密度: {features.get('edge_density', 0):.3f}
黑色像素比例: {features.get('black_pixel_ratio', 0):.3f}
"""
        return info

    def validate_label(self, label: str) -> bool:
        """验证标注是否有效"""
        # 检查是否为4位数字
        if len(label) != 4:
            return False

        if not label.isdigit():
            return False

        return True

    def add_label(self, filename: str, label: str, confidence: float = 1.0) -> bool:
        """添加标注"""
        if not self.validate_label(label):
            logger.error(f"无效标注: {label}")
            return False

        # 添加标注
        self.labels["labels"][filename] = {
            "label": label,
            "confidence": confidence,
            "labeled_time": datetime.now().isoformat(),
            "length": len(label)
        }

        # 更新统计信息
        self.labels["labeled_count"] = len(self.labels["labels"])

        # 更新数字频率统计
        for digit in label:
            if digit in self.labels["statistics"]["digit_frequency"]:
                self.labels["statistics"]["digit_frequency"][digit] += 1

        # 更新长度分布
        length = str(len(label))
        if length in self.labels["statistics"]["length_distribution"]:
            self.labels["statistics"]["length_distribution"][length] += 1
        else:
            self.labels["statistics"]["length_distribution"][length] = 1

        logger.info(f"添加标注: {filename} -> {label}")
        return True

    def batch_label_interactive(self, batch_size: int = 50) -> Dict[str, Any]:
        """交互式批量标注"""
        unlabeled = self.get_unlabeled_captchas()

        if not unlabeled:
            return {
                "status": "completed",
                "message": "没有未标注的验证码",
                "labeled_count": 0
            }

        session_info = {
            "session_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "start_time": datetime.now().isoformat(),
            "target_count": min(batch_size, len(unlabeled)),
            "labeled_count": 0,
            "skipped_count": 0,
            "status": "running"
        }

        logger.info(f"开始交互式标注，目标数量: {session_info['target_count']}")
        logger.info("输入说明:")
        logger.info("- 输入4位数字进行标注")
        logger.info("- 输入 's' 跳过当前验证码")
        logger.info("- 输入 'q' 退出标注")
        logger.info("- 输入 'stats' 查看统计信息")

        try:
            for i, captcha_info in enumerate(unlabeled[:batch_size]):
                filename = captcha_info.get("filename", "")
                filepath = captcha_info.get("filepath", "")

                print(f"\n进度: {i + 1}/{session_info['target_count']}")
                print(self.display_captcha_info(captcha_info))
                print(f"图片路径: {filepath}")

                # 显示图片（优化版本）
                self.display_captcha_image(filepath, filename)

                while True:
                    user_input = input("请输入验证码内容 (4位数字): ").strip()

                    if user_input.lower() == 'q':
                        logger.info("用户退出标注")
                        session_info["status"] = "interrupted"
                        cv2.destroyAllWindows()
                        return session_info

                    elif user_input.lower() == 's':
                        logger.info(f"跳过验证码: {filename}")
                        session_info["skipped_count"] += 1
                        break

                    elif user_input.lower() == 'stats':
                        self.print_statistics()
                        continue

                    elif self.validate_label(user_input):
                        if self.add_label(filename, user_input):
                            session_info["labeled_count"] += 1
                            break
                        else:
                            print("标注添加失败，请重试")

                    else:
                        print("无效输入，请输入4位数字")

                # 每10个标注保存一次
                if (i + 1) % 10 == 0:
                    self.save_labels()
                    logger.info(f"已保存 {i + 1} 个标注")

                # 关闭图片窗口
                cv2.destroyAllWindows()

        except KeyboardInterrupt:
            logger.info("用户中断标注")
            session_info["status"] = "interrupted"

        except Exception as e:
            logger.error(f"标注过程发生错误: {e}")
            session_info["status"] = "error"
            session_info["error"] = str(e)

        finally:
            cv2.destroyAllWindows()
            session_info["end_time"] = datetime.now().isoformat()
            self.save_labels()

            # 记录标注会话
            self.labels["statistics"]["labeling_sessions"].append(session_info)
            self.save_labels()

        logger.info(f"标注完成！已标注: {session_info['labeled_count']}, 跳过: {session_info['skipped_count']}")
        return session_info

    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_labeling_statistics()

        print("\n=== 标注统计信息 ===")
        print(f"总收集数量: {stats['total_collected']}")
        print(f"已标注数量: {stats['labeled_count']}")
        print(f"未标注数量: {stats['unlabeled_count']}")
        print(f"标注进度: {stats['labeling_progress']:.1f}%")

        print("\n数字频率分布:")
        for digit, count in stats['digit_frequency'].items():
            print(f"  {digit}: {count} 次")

        print(f"\n长度分布:")
        for length, count in stats['length_distribution'].items():
            print(f"  {length}位: {count} 个")

    def get_labeling_statistics(self) -> Dict[str, Any]:
        """获取标注统计信息"""
        total_collected = len(self.metadata.get("captcha_files", []))
        labeled_count = self.labels.get("labeled_count", 0)
        unlabeled_count = total_collected - labeled_count

        stats = {
            "total_collected": total_collected,
            "labeled_count": labeled_count,
            "unlabeled_count": unlabeled_count,
            "labeling_progress": (labeled_count / total_collected * 100) if total_collected > 0 else 0,
            "digit_frequency": self.labels.get("statistics", {}).get("digit_frequency", {}),
            "length_distribution": self.labels.get("statistics", {}).get("length_distribution", {}),
            "sessions_count": len(self.labels.get("statistics", {}).get("labeling_sessions", []))
        }

        return stats

    def export_training_data(self) -> Dict[str, Any]:
        """导出训练数据"""
        labeled_data = []

        for filename, label_info in self.labels.get("labels", {}).items():
            # 查找对应的验证码文件信息
            captcha_info = None
            for info in self.metadata.get("captcha_files", []):
                if info.get("filename") == filename:
                    captcha_info = info
                    break

            if captcha_info and os.path.exists(captcha_info.get("filepath", "")):
                labeled_data.append({
                    "filename": filename,
                    "filepath": captcha_info.get("filepath"),
                    "label": label_info.get("label"),
                    "confidence": label_info.get("confidence", 1.0),
                    "features": captcha_info.get("features", {}),
                    "labeled_time": label_info.get("labeled_time")
                })

        # 保存训练数据
        training_data_file = os.path.join(self.labeled_dir, "training_data.json")
        try:
            with open(training_data_file, 'w', encoding='utf-8') as f:
                json.dump(labeled_data, f, ensure_ascii=False, indent=2)

            logger.info(f"训练数据已导出: {training_data_file}")
            logger.info(f"包含 {len(labeled_data)} 个标注样本")

            return {
                "status": "success",
                "training_data_file": training_data_file,
                "sample_count": len(labeled_data),
                "export_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"导出训练数据失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

# 全局实例
captcha_labeler = CaptchaLabeler()
