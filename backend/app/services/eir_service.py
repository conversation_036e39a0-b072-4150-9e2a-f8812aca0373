from typing import Dict, Any, Optional
from ..automation.eir_automation import EIRAutomation
from ..models.database import DatabaseManager

class EIRService:
    def __init__(self):
        self.automation = EIRAutomation()
        self.db = DatabaseManager()

    async def get_login_captcha(self) -> Dict[str, Any]:
        """获取登录验证码"""
        try:
            captcha_path = self.automation.get_captcha_for_login()
            if captcha_path:
                return {
                    "success": True,
                    "captcha_path": captcha_path,
                    "message": "验证码获取成功"
                }
            else:
                return {
                    "success": False,
                    "message": "验证码获取失败"
                }
        except Exception as e:
            return {
                "success": False,
                "message": f"获取验证码时发生错误: {str(e)}"
            }

    async def refresh_captcha(self) -> Dict[str, Any]:
        """刷新验证码"""
        try:
            captcha_path = self.automation.refresh_captcha()
            if captcha_path:
                return {
                    "success": True,
                    "captcha_path": captcha_path,
                    "message": "验证码刷新成功"
                }
            else:
                return {
                    "success": False,
                    "message": "验证码刷新失败"
                }
        except Exception as e:
            return {
                "success": False,
                "message": f"刷新验证码时发生错误: {str(e)}"
            }

    async def login(self, captcha_code: str) -> Dict[str, Any]:
        """执行登录"""
        try:
            success, message = self.automation.login(captcha_code)
            return {
                "success": success,
                "message": message
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"登录时发生错误: {str(e)}"
            }

    async def auto_login(self) -> Dict[str, Any]:
        """自动登录（包括自动验证码识别）"""
        try:
            success, message = self.automation.auto_login()
            return {
                "success": success,
                "message": message
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"自动登录时发生错误: {str(e)}"
            }

    async def recognize_captcha(self, captcha_path: str) -> Dict[str, Any]:
        """识别验证码"""
        try:
            from .captcha_service import captcha_service
            result = captcha_service.recognize_captcha(captcha_path)
            return result
        except Exception as e:
            return {
                "success": False,
                "message": f"验证码识别时发生错误: {str(e)}"
            }

    async def check_login_status(self) -> Dict[str, Any]:
        """检查登录状态"""
        try:
            is_logged_in = self.automation.check_login_status()
            return {
                "success": True,
                "is_logged_in": is_logged_in,
                "message": "已登录" if is_logged_in else "未登录"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"检查登录状态时发生错误: {str(e)}"
            }

    def close(self):
        """关闭服务"""
        self.automation.close()
