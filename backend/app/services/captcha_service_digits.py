"""
专门针对4位数字验证码的高精度识别服务
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import requests
import base64
from typing import Optional, Dict, Any, List, Tuple
import logging
import itertools

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DigitCaptchaService:
    def __init__(self):
        self.proxy_config = self.get_proxy_config()
        # 数字模板 - 可以根据实际验证码字体调整
        self.digit_patterns = self.init_digit_patterns()
    
    def get_proxy_config(self) -> Dict[str, str]:
        """获取代理配置"""
        proxy_url = "http://127.0.0.1:7897"
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def init_digit_patterns(self) -> Dict[str, List[Tuple]]:
        """初始化数字特征模式"""
        # 基于常见验证码字体的数字特征
        patterns = {
            '0': [
                ('oval', 'closed'),  # 椭圆形，封闭
                ('holes', 1),        # 有1个洞
                ('width_ratio', 0.6, 0.8),  # 宽高比
            ],
            '1': [
                ('vertical_lines', 'strong'),  # 强垂直线
                ('holes', 0),                  # 无洞
                ('width_ratio', 0.2, 0.5),    # 较窄
            ],
            '2': [
                ('curves', 'top_bottom'),  # 上下有弯曲
                ('holes', 0),              # 无洞
                ('horizontal_lines', 'middle_bottom'),  # 中下有横线
            ],
            '3': [
                ('curves', 'right'),       # 右侧弯曲
                ('holes', 0),              # 无洞
                ('horizontal_lines', 'middle'),  # 中间有横线
            ],
            '4': [
                ('vertical_lines', 'left_right'),  # 左右垂直线
                ('horizontal_lines', 'middle'),    # 中间横线
                ('holes', 0),                      # 无洞
            ],
            '5': [
                ('horizontal_lines', 'top_bottom'),  # 上下横线
                ('curves', 'bottom_right'),          # 右下弯曲
                ('holes', 0),                        # 无洞
            ],
            '6': [
                ('curves', 'left'),       # 左侧弯曲
                ('holes', 1),             # 有1个洞
                ('closed_bottom', True),  # 底部封闭
            ],
            '7': [
                ('horizontal_lines', 'top'),  # 顶部横线
                ('diagonal_lines', True),     # 有斜线
                ('holes', 0),                 # 无洞
            ],
            '8': [
                ('holes', 2),             # 有2个洞
                ('symmetrical', True),    # 对称
                ('curves', 'both'),       # 上下都有弯曲
            ],
            '9': [
                ('holes', 1),             # 有1个洞
                ('curves', 'top'),        # 顶部弯曲
                ('open_bottom', True),    # 底部开放
            ]
        }
        return patterns
    
    def preprocess_image(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """高级图像预处理，专门针对数字验证码"""
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                logger.error(f"无法读取图像: {image_path}")
                return None, None
            
            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 1. 去噪处理
            # 高斯模糊
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 中值滤波去除椒盐噪声
            denoised = cv2.medianBlur(blurred, 3)
            
            # 2. 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # 3. 二值化处理
            # 使用多种阈值方法
            _, binary1 = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            binary2 = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            binary3 = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2)
            
            # 选择最佳二值化结果
            binaries = [binary1, binary2, binary3]
            best_binary = self.select_best_binary(binaries)
            
            # 4. 形态学操作
            # 去除小噪点
            kernel = np.ones((2,2), np.uint8)
            cleaned = cv2.morphologyEx(best_binary, cv2.MORPH_OPEN, kernel)
            
            # 连接断开的线条
            kernel2 = np.ones((1,2), np.uint8)
            connected = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel2)
            
            # 5. 反转图像（确保文字为白色，背景为黑色）
            if np.mean(connected) > 127:
                connected = cv2.bitwise_not(connected)
            
            logger.info(f"图像预处理完成: {image_path}")
            return enhanced, connected
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return None, None
    
    def select_best_binary(self, binaries: List[np.ndarray]) -> np.ndarray:
        """选择最佳的二值化结果"""
        scores = []
        for binary in binaries:
            # 计算连通组件数量
            num_labels, _ = cv2.connectedComponents(binary)
            # 计算边缘密度
            edges = cv2.Canny(binary, 50, 150)
            edge_density = np.sum(edges) / (binary.shape[0] * binary.shape[1])
            # 综合评分（希望有适中的连通组件数量和边缘密度）
            score = abs(num_labels - 5) * 0.3 + edge_density * 0.7  # 期望4个数字+背景=5个组件
            scores.append(score)
        
        best_idx = np.argmax(scores)
        return binaries[best_idx]
    
    def segment_digits(self, binary_img: np.ndarray) -> List[np.ndarray]:
        """分割单个数字"""
        try:
            # 查找轮廓
            contours, _ = cv2.findContours(binary_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            valid_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                # 过滤条件：面积、宽高比
                if area > 50 and 0.2 <= aspect_ratio <= 2.0 and h > 10:
                    valid_contours.append((x, contour))
            
            # 按x坐标排序（从左到右）
            valid_contours.sort(key=lambda x: x[0])
            
            # 提取数字区域
            digit_images = []
            for x, contour in valid_contours:
                x, y, w, h = cv2.boundingRect(contour)
                # 添加一些边距
                margin = 2
                x1 = max(0, x - margin)
                y1 = max(0, y - margin)
                x2 = min(binary_img.shape[1], x + w + margin)
                y2 = min(binary_img.shape[0], y + h + margin)
                
                digit_roi = binary_img[y1:y2, x1:x2]
                if digit_roi.size > 0:
                    digit_images.append(digit_roi)
            
            logger.info(f"分割出 {len(digit_images)} 个数字区域")
            return digit_images
            
        except Exception as e:
            logger.error(f"数字分割失败: {e}")
            return []
    
    def analyze_digit_features(self, digit_img: np.ndarray) -> Dict[str, Any]:
        """分析单个数字的特征"""
        try:
            features = {}
            
            # 基本尺寸特征
            h, w = digit_img.shape
            features['width'] = w
            features['height'] = h
            features['aspect_ratio'] = w / h if h > 0 else 0
            
            # 像素密度
            white_pixels = np.sum(digit_img == 255)
            total_pixels = h * w
            features['density'] = white_pixels / total_pixels if total_pixels > 0 else 0
            
            # 洞的数量（连通组件分析）
            # 反转图像进行洞检测
            inverted = cv2.bitwise_not(digit_img)
            num_labels, _ = cv2.connectedComponents(inverted)
            features['holes'] = max(0, num_labels - 2)  # 减去背景和主体
            
            # 水平和垂直投影
            h_projection = np.sum(digit_img, axis=1)  # 水平投影
            v_projection = np.sum(digit_img, axis=0)  # 垂直投影
            
            features['h_projection'] = h_projection.tolist()
            features['v_projection'] = v_projection.tolist()
            
            # 投影特征分析
            features['h_peaks'] = len([i for i in range(1, len(h_projection)-1) 
                                     if h_projection[i] > h_projection[i-1] and h_projection[i] > h_projection[i+1]])
            features['v_peaks'] = len([i for i in range(1, len(v_projection)-1) 
                                     if v_projection[i] > v_projection[i-1] and v_projection[i] > v_projection[i+1]])
            
            # 边缘特征
            edges = cv2.Canny(digit_img, 50, 150)
            features['edge_density'] = np.sum(edges) / total_pixels if total_pixels > 0 else 0
            
            return features
            
        except Exception as e:
            logger.error(f"特征分析失败: {e}")
            return {}
    
    def match_digit(self, features: Dict[str, Any]) -> List[Tuple[str, float]]:
        """根据特征匹配数字"""
        try:
            scores = []
            
            for digit in '0123456789':
                score = 0.0
                
                # 基于洞的数量匹配
                holes = features.get('holes', 0)
                if digit in ['0', '4', '6', '9'] and holes == 1:
                    score += 0.3
                elif digit == '8' and holes == 2:
                    score += 0.4
                elif digit in ['1', '2', '3', '5', '7'] and holes == 0:
                    score += 0.2
                
                # 基于宽高比匹配
                aspect_ratio = features.get('aspect_ratio', 0.5)
                if digit == '1' and aspect_ratio < 0.6:
                    score += 0.3
                elif digit in ['0', '8'] and 0.6 <= aspect_ratio <= 0.9:
                    score += 0.2
                elif digit in ['2', '3', '4', '5', '6', '7', '9'] and 0.4 <= aspect_ratio <= 0.8:
                    score += 0.1
                
                # 基于密度匹配
                density = features.get('density', 0.3)
                if digit in ['1', '7'] and density < 0.4:
                    score += 0.2
                elif digit in ['0', '8'] and density > 0.3:
                    score += 0.1
                
                # 基于投影峰值匹配
                h_peaks = features.get('h_peaks', 0)
                v_peaks = features.get('v_peaks', 0)
                
                if digit == '1' and v_peaks <= 2:
                    score += 0.2
                elif digit in ['2', '3', '5'] and h_peaks >= 2:
                    score += 0.1
                elif digit == '4' and h_peaks >= 1 and v_peaks >= 2:
                    score += 0.2
                
                scores.append((digit, score))
            
            # 按分数排序
            scores.sort(key=lambda x: x[1], reverse=True)
            return scores
            
        except Exception as e:
            logger.error(f"数字匹配失败: {e}")
            return []
    
    def recognize_digits(self, image_path: str) -> List[str]:
        """识别4位数字验证码"""
        try:
            # 预处理图像
            enhanced, binary = self.preprocess_image(image_path)
            if binary is None:
                return []
            
            # 分割数字
            digit_images = self.segment_digits(binary)
            
            # 如果分割出的数字不是4个，尝试其他方法
            if len(digit_images) != 4:
                logger.warning(f"分割出 {len(digit_images)} 个数字，期望4个")
                # 尝试固定宽度分割
                digit_images = self.segment_by_width(binary)
            
            if len(digit_images) == 0:
                return []
            
            # 识别每个数字
            recognized_digits = []
            for i, digit_img in enumerate(digit_images):
                features = self.analyze_digit_features(digit_img)
                matches = self.match_digit(features)
                
                if matches:
                    best_digit = matches[0][0]
                    confidence = matches[0][1]
                    recognized_digits.append(best_digit)
                    logger.info(f"数字 {i+1}: {best_digit} (置信度: {confidence:.2f})")
                else:
                    # 如果无法识别，使用常见数字
                    recognized_digits.append('0')
                    logger.warning(f"数字 {i+1}: 无法识别，使用默认值 0")
            
            return recognized_digits
            
        except Exception as e:
            logger.error(f"数字识别失败: {e}")
            return []
    
    def segment_by_width(self, binary_img: np.ndarray) -> List[np.ndarray]:
        """按固定宽度分割数字（备用方法）"""
        try:
            h, w = binary_img.shape
            digit_width = w // 4  # 假设4个数字等宽分布
            
            digit_images = []
            for i in range(4):
                x1 = i * digit_width
                x2 = (i + 1) * digit_width if i < 3 else w
                digit_roi = binary_img[:, x1:x2]
                if digit_roi.size > 0:
                    digit_images.append(digit_roi)
            
            logger.info(f"按宽度分割出 {len(digit_images)} 个数字区域")
            return digit_images
            
        except Exception as e:
            logger.error(f"按宽度分割失败: {e}")
            return []
    
    def generate_digit_combinations(self, recognized_digits: List[str]) -> List[str]:
        """生成数字组合候选"""
        if len(recognized_digits) == 4:
            # 如果识别出4个数字，生成主要候选和变体
            main_code = ''.join(recognized_digits)
            candidates = [main_code]
            
            # 生成相似数字的变体
            similar_digits = {
                '0': ['0', '8', '6'],
                '1': ['1', '7', 'I'],
                '2': ['2', 'Z'],
                '3': ['3', '8'],
                '4': ['4', 'A'],
                '5': ['5', 'S'],
                '6': ['6', '0', '8'],
                '7': ['7', '1', 'T'],
                '8': ['8', '0', '3', '6'],
                '9': ['9', '6']
            }
            
            # 为每个位置生成变体
            for i in range(4):
                original_digit = recognized_digits[i]
                if original_digit in similar_digits:
                    for similar in similar_digits[original_digit][1:2]:  # 只取第一个相似数字
                        if similar.isdigit():
                            variant = list(recognized_digits)
                            variant[i] = similar
                            candidates.append(''.join(variant))
            
            return candidates[:5]  # 返回前5个候选
        else:
            # 如果识别数字不足4个，生成常见的4位数字组合
            return self.generate_common_codes()
    
    def generate_common_codes(self) -> List[str]:
        """生成常见的4位验证码"""
        # 基于常见模式生成
        common_patterns = []
        
        # 连续数字
        for start in range(7):
            common_patterns.append(''.join(str((start + i) % 10) for i in range(4)))
        
        # 重复数字
        for digit in '0123456789':
            if digit not in ['0', '1']:  # 避免0000, 1111这种简单模式
                common_patterns.append(digit * 4)
        
        # 常见年份
        for year in ['2024', '2025', '1234', '5678', '9876']:
            common_patterns.append(year)
        
        return common_patterns[:10]
    
    def recognize_captcha(self, image_path: str) -> Dict[str, Any]:
        """识别验证码主函数"""
        if not os.path.exists(image_path):
            return {
                'success': False,
                'message': '验证码图片不存在',
                'best_result': None,
                'all_results': []
            }
        
        try:
            # 识别数字
            recognized_digits = self.recognize_digits(image_path)
            
            if not recognized_digits:
                return {
                    'success': False,
                    'message': '无法识别数字',
                    'best_result': None,
                    'all_results': []
                }
            
            # 生成候选组合
            candidates = self.generate_digit_combinations(recognized_digits)
            
            if not candidates:
                return {
                    'success': False,
                    'message': '无法生成候选验证码',
                    'best_result': None,
                    'all_results': []
                }
            
            best_result = candidates[0]
            confidence = 0.8 if len(recognized_digits) == 4 else 0.4
            
            logger.info(f"验证码识别完成: {best_result}, 候选: {candidates}")
            
            return {
                'success': True,
                'message': '验证码识别成功',
                'best_result': best_result,
                'all_results': candidates,
                'confidence': confidence,
                'recognized_digits': recognized_digits
            }
            
        except Exception as e:
            logger.error(f"验证码识别异常: {e}")
            return {
                'success': False,
                'message': f'识别过程发生错误: {str(e)}',
                'best_result': None,
                'all_results': []
            }

# 全局实例
digit_captcha_service = DigitCaptchaService()
