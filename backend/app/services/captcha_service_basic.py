"""
基础验证码识别服务 - 不依赖OCR，使用图像处理和模式匹配
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import requests
import base64
from typing import Optional, Dict, Any, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BasicCaptchaService:
    def __init__(self):
        self.proxy_config = self.get_proxy_config()

    def get_proxy_config(self) -> Dict[str, str]:
        """获取代理配置"""
        proxy_url = "http://127.0.0.1:7897"
        return {
            'http': proxy_url,
            'https': proxy_url
        }

    def preprocess_image(self, image_path: str) -> Optional[np.ndarray]:
        """图像预处理"""
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                logger.error(f"无法读取图像: {image_path}")
                return None

            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # 自适应阈值二值化
            binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

            # 形态学操作去除噪点
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            return cleaned

        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return None

    def analyze_image_features(self, image_path: str) -> Dict[str, Any]:
        """分析图像特征"""
        try:
            processed_img = self.preprocess_image(image_path)
            if processed_img is None:
                return {}

            # 获取图像尺寸
            height, width = processed_img.shape

            # 计算黑白像素比例
            total_pixels = height * width
            black_pixels = np.sum(processed_img == 0)
            white_pixels = total_pixels - black_pixels
            black_ratio = black_pixels / total_pixels

            # 查找轮廓
            contours, _ = cv2.findContours(processed_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 分析轮廓特征
            contour_areas = [cv2.contourArea(c) for c in contours if cv2.contourArea(c) > 10]
            contour_count = len(contour_areas)

            # 估计字符数量（基于轮廓数量）
            estimated_chars = min(max(contour_count, 3), 6)  # 验证码通常3-6位

            features = {
                'width': width,
                'height': height,
                'black_ratio': black_ratio,
                'contour_count': contour_count,
                'estimated_chars': estimated_chars,
                'contour_areas': contour_areas
            }

            logger.info(f"图像特征: {features}")
            return features

        except Exception as e:
            logger.error(f"图像特征分析失败: {e}")
            return {}

    def generate_possible_codes(self, features: Dict[str, Any]) -> List[str]:
        """基于图像特征生成可能的验证码"""
        possible_codes = []

        try:
            estimated_chars = features.get('estimated_chars', 4)

            # 常见的验证码模式
            patterns = [
                # 纯数字
                ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
                # 数字+字母
                ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K',
                 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
                 'W', 'X', 'Y', 'Z']  # 排除容易混淆的I, O
            ]

            # 基于轮廓数量和黑白比例生成候选
            black_ratio = features.get('black_ratio', 0.3)

            if black_ratio > 0.4:
                # 黑色像素较多，可能是粗体字符
                candidates = ['8', '0', 'B', 'D', 'P', 'R']
            elif black_ratio < 0.2:
                # 黑色像素较少，可能是细体字符
                candidates = ['1', 'I', 'L', 'T', 'F', 'E']
            else:
                # 中等比例，常见字符
                candidates = ['2', '3', '4', '5', '6', '7', '9', 'A', 'C', 'G', 'H', 'J', 'K', 'M', 'N', 'Q', 'S', 'U', 'V', 'W', 'X', 'Y', 'Z']

            # 生成可能的组合
            import itertools
            for length in range(max(3, estimated_chars-1), min(7, estimated_chars+2)):
                for combo in itertools.product(candidates, repeat=length):
                    code = ''.join(combo)
                    possible_codes.append(code)
                    if len(possible_codes) >= 20:  # 限制候选数量
                        break
                if len(possible_codes) >= 20:
                    break

            logger.info(f"生成了 {len(possible_codes)} 个候选验证码")
            return possible_codes[:10]  # 返回前10个最可能的

        except Exception as e:
            logger.error(f"生成候选验证码失败: {e}")
            return []

    def recognize_with_online_api(self, image_path: str) -> List[str]:
        """使用在线API识别验证码"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode()

            # 尝试使用免费的OCR.space API
            api_url = "https://api.ocr.space/parse/image"
            payload = {
                'base64Image': f'data:image/png;base64,{image_base64}',
                'language': 'eng',
                'isOverlayRequired': False,
                'OCREngine': 2
            }

            response = requests.post(api_url, data=payload, proxies=self.proxy_config, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('IsErroredOnProcessing') == False:
                    parsed_text = result.get('ParsedResults', [{}])[0].get('ParsedText', '').strip()
                    # 清理识别结果
                    cleaned_text = ''.join(c for c in parsed_text if c.isalnum())
                    if cleaned_text and len(cleaned_text) >= 3 and len(cleaned_text) <= 6:
                        logger.info(f"在线API识别结果: {cleaned_text}")
                        return [cleaned_text]

            return []

        except Exception as e:
            logger.error(f"在线API识别失败: {e}")
            return []

    def recognize_captcha(self, image_path: str) -> Dict[str, Any]:
        """识别验证码"""
        if not os.path.exists(image_path):
            return {
                'success': False,
                'message': '验证码图片不存在',
                'best_result': None,
                'all_results': []
            }

        all_results = []

        # 1. 分析图像特征
        features = self.analyze_image_features(image_path)
        if not features:
            return {
                'success': False,
                'message': '图像特征分析失败',
                'best_result': None,
                'all_results': []
            }

        # 2. 尝试在线API识别
        try:
            online_results = self.recognize_with_online_api(image_path)
            all_results.extend(online_results)
        except:
            pass

        # 3. 基于特征生成候选
        candidates = self.generate_possible_codes(features)

        # 如果有在线识别结果，优先使用
        if all_results:
            best_result = all_results[0]
            confidence = 0.8
        elif candidates:
            # 使用第一个候选作为最佳结果
            best_result = candidates[0]
            confidence = 0.3
            all_results = candidates[:5]  # 返回前5个候选
        else:
            return {
                'success': False,
                'message': '无法生成验证码候选',
                'best_result': None,
                'all_results': []
            }

        return {
            'success': True,
            'message': '验证码识别完成',
            'best_result': best_result,
            'all_results': all_results,
            'confidence': confidence,
            'features': features
        }

# 全局实例
basic_captcha_service = BasicCaptchaService()
