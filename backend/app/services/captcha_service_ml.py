"""
基于机器学习模型的验证码识别服务
集成训练好的高精度模型
"""

import os
import cv2
import numpy as np
from typing import Optional, Dict, Any, List
import logging
from .captcha_trainer import captcha_trainer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLCaptchaService:
    def __init__(self):
        self.models = None
        self.load_models()
    
    def load_models(self):
        """加载训练好的模型"""
        try:
            self.models = captcha_trainer.load_trained_models()
            if self.models:
                logger.info("✅ 机器学习模型加载成功")
                # 检查模型完整性
                model_count = len([k for k in self.models.keys() if 'position_' in k])
                has_whole_model = 'whole_model' in self.models
                logger.info(f"📊 加载了 {model_count} 个分位置模型，整体模型: {'是' if has_whole_model else '否'}")
            else:
                logger.error("❌ 模型加载失败")
        except Exception as e:
            logger.error(f"❌ 模型加载异常: {e}")
            self.models = None
    
    def recognize_captcha(self, image_path: str) -> Dict[str, Any]:
        """
        使用机器学习模型识别验证码
        
        Args:
            image_path: 验证码图片路径
            
        Returns:
            Dict包含识别结果和置信度
        """
        try:
            if not self.models:
                logger.warning("⚠️ 模型未加载，尝试重新加载")
                self.load_models()
                if not self.models:
                    return self._fallback_recognition(image_path)
            
            # 使用训练好的模型进行预测
            result = captcha_trainer.predict_captcha(image_path, self.models)
            
            if result.get("status") == "success":
                best_prediction = result.get("best_prediction")
                best_confidence = result.get("best_confidence", 0)
                all_predictions = result.get("all_predictions", {})
                
                # 生成候选列表
                candidates = []
                
                # 添加最佳预测
                if best_prediction:
                    candidates.append(best_prediction)
                
                # 添加其他模型的预测结果
                for model_name, pred_info in all_predictions.items():
                    prediction = pred_info.get("prediction")
                    if prediction and prediction != best_prediction:
                        candidates.append(prediction)
                
                # 移除重复项，保持顺序
                unique_candidates = []
                for candidate in candidates:
                    if candidate not in unique_candidates:
                        unique_candidates.append(candidate)
                
                # 如果候选太少，生成一些变体
                if len(unique_candidates) < 3:
                    unique_candidates.extend(self._generate_variants(best_prediction))
                
                # 限制候选数量
                final_candidates = unique_candidates[:5]
                
                logger.info(f"🎯 ML识别结果: {best_prediction} (置信度: {best_confidence:.3f})")
                logger.info(f"📋 候选列表: {final_candidates}")
                
                return {
                    "success": True,
                    "best_result": best_prediction,
                    "confidence": best_confidence,
                    "all_results": final_candidates,
                    "method": "machine_learning",
                    "model_info": {
                        "models_loaded": len([k for k in self.models.keys() if 'position_' in k]),
                        "predictions": all_predictions
                    }
                }
            else:
                logger.warning(f"⚠️ ML模型预测失败: {result.get('message')}")
                return self._fallback_recognition(image_path)
                
        except Exception as e:
            logger.error(f"❌ ML识别过程异常: {e}")
            return self._fallback_recognition(image_path)
    
    def _generate_variants(self, base_prediction: str) -> List[str]:
        """生成预测变体"""
        if not base_prediction or len(base_prediction) != 4:
            return []
        
        variants = []
        
        # 常见的数字混淆对
        confusions = {
            '0': ['8', '6', '9'],
            '1': ['7', 'I', 'l'],
            '2': ['Z', '5'],
            '3': ['8', '5'],
            '4': ['A', '9'],
            '5': ['S', '2', '3'],
            '6': ['G', '0', '8'],
            '7': ['1', 'T'],
            '8': ['B', '0', '3', '6'],
            '9': ['g', '4', '0']
        }
        
        # 为每个位置生成一个变体
        for i, digit in enumerate(base_prediction):
            if digit in confusions:
                for alt_digit in confusions[digit][:1]:  # 只取第一个替代
                    if alt_digit.isdigit():
                        variant = base_prediction[:i] + alt_digit + base_prediction[i+1:]
                        if variant not in variants:
                            variants.append(variant)
        
        return variants[:3]  # 最多返回3个变体
    
    def _fallback_recognition(self, image_path: str) -> Dict[str, Any]:
        """备用识别方法"""
        logger.info("🔄 使用备用识别方法")
        
        try:
            # 使用基础的图像处理方法
            result = self._basic_ocr_recognition(image_path)
            if result.get("success"):
                return result
        except Exception as e:
            logger.error(f"❌ 备用识别失败: {e}")
        
        # 最后的备用方案
        return {
            "success": False,
            "message": "所有识别方法都失败了",
            "best_result": None,
            "confidence": 0.0,
            "all_results": [],
            "method": "failed"
        }
    
    def _basic_ocr_recognition(self, image_path: str) -> Dict[str, Any]:
        """基础OCR识别"""
        try:
            import cv2
            
            # 读取图片
            img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                return {"success": False, "message": "无法读取图片"}
            
            # 图像预处理
            # 调整尺寸
            height, width = img.shape
            if width < 100:  # 如果图片太小，放大
                scale = 100 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                img = cv2.resize(img, (new_width, new_height))
            
            # 去噪
            img = cv2.medianBlur(img, 3)
            
            # 二值化
            _, img = cv2.threshold(img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 尝试使用EasyOCR（如果可用）
            try:
                import easyocr
                reader = easyocr.Reader(['en'], gpu=False)
                results = reader.readtext(img)
                
                for (bbox, text, confidence) in results:
                    # 过滤出4位数字
                    clean_text = ''.join(c for c in text if c.isdigit())
                    if len(clean_text) == 4:
                        return {
                            "success": True,
                            "best_result": clean_text,
                            "confidence": confidence,
                            "all_results": [clean_text],
                            "method": "easyocr_fallback"
                        }
            except ImportError:
                logger.info("EasyOCR不可用，跳过")
            except Exception as e:
                logger.warning(f"EasyOCR识别失败: {e}")
            
            # 如果EasyOCR失败，返回失败结果
            return {
                "success": False,
                "message": "基础OCR识别失败",
                "method": "basic_ocr_failed"
            }
            
        except Exception as e:
            logger.error(f"基础OCR异常: {e}")
            return {"success": False, "message": f"基础OCR异常: {e}"}
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.models:
            return {"status": "not_loaded"}
        
        info = {
            "status": "loaded",
            "models_count": len([k for k in self.models.keys() if 'position_' in k]),
            "has_whole_model": 'whole_model' in self.models,
            "training_info": self.models.get("training_info", {})
        }
        
        return info
    
    def test_model(self, test_image_path: str = None) -> Dict[str, Any]:
        """测试模型性能"""
        if not self.models:
            return {"status": "error", "message": "模型未加载"}
        
        # 如果没有指定测试图片，使用收集的验证码
        if not test_image_path:
            collection_dir = "data/captcha_collection"
            if os.path.exists(collection_dir):
                test_files = [f for f in os.listdir(collection_dir) if f.endswith('.png')]
                if test_files:
                    test_image_path = os.path.join(collection_dir, test_files[0])
        
        if not test_image_path or not os.path.exists(test_image_path):
            return {"status": "error", "message": "没有找到测试图片"}
        
        # 进行测试
        result = self.recognize_captcha(test_image_path)
        
        return {
            "status": "success",
            "test_image": test_image_path,
            "recognition_result": result,
            "model_info": self.get_model_info()
        }

# 全局实例
ml_captcha_service = MLCaptchaService()
