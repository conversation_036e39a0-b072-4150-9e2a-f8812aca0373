"""
智能代理管理器
根据GFWList决定是否使用代理
"""

import re
import requests
from typing import Dict, Optional, Set
import logging
from urllib.parse import urlparse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProxyManager:
    def __init__(self):
        self.proxy_host = "127.0.0.1"
        self.proxy_port = "7897"
        self.blocked_domains = set()
        self.blocked_patterns = []
        self.load_gfwlist()
    
    def load_gfwlist(self):
        """加载GFWList规则"""
        try:
            # 常见被墙的域名和模式
            common_blocked = {
                # 搜索引擎
                'google.com', 'google.com.hk', 'googleapis.com', 'googleusercontent.com',
                'youtube.com', 'ytimg.com', 'googlevideo.com',
                
                # 社交媒体
                'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
                'telegram.org', 'whatsapp.com',
                
                # 开发工具
                'github.com', 'githubusercontent.com', 'github.io',
                'stackoverflow.com', 'stackexchange.com',
                
                # 其他服务
                'dropbox.com', 'onedrive.com', 'medium.com',
                'wikipedia.org', 'wikimedia.org',
                
                # OCR服务
                'ocr.space', 'api.ocr.space'
            }
            
            self.blocked_domains.update(common_blocked)
            
            # 添加模式匹配
            self.blocked_patterns = [
                r'.*\.google\..*',
                r'.*\.youtube\..*',
                r'.*\.facebook\..*',
                r'.*\.twitter\..*',
                r'.*\.github\..*',
                r'.*\.googleapis\..*'
            ]
            
            logger.info(f"加载了 {len(self.blocked_domains)} 个被墙域名")
            
        except Exception as e:
            logger.error(f"加载GFWList失败: {e}")
    
    def is_blocked_domain(self, url: str) -> bool:
        """检查域名是否被墙"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # 移除端口号
            if ':' in domain:
                domain = domain.split(':')[0]
            
            # 检查完整域名
            if domain in self.blocked_domains:
                return True
            
            # 检查父域名
            parts = domain.split('.')
            for i in range(len(parts)):
                parent_domain = '.'.join(parts[i:])
                if parent_domain in self.blocked_domains:
                    return True
            
            # 检查模式匹配
            for pattern in self.blocked_patterns:
                if re.match(pattern, domain):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查域名失败: {e}")
            return False
    
    def get_proxy_config(self, url: str = None) -> Dict[str, str]:
        """获取代理配置"""
        if url and not self.is_blocked_domain(url):
            # 国内网站不使用代理
            return {}
        
        # 被墙网站使用代理
        proxy_url = f"http://{self.proxy_host}:{self.proxy_port}"
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def get_chrome_proxy_args(self, url: str = None) -> list:
        """获取Chrome代理参数"""
        if url and not self.is_blocked_domain(url):
            # 国内网站不使用代理
            return []
        
        # 被墙网站使用代理
        proxy_url = f"{self.proxy_host}:{self.proxy_port}"
        return [f"--proxy-server=http://{proxy_url}"]
    
    def should_use_proxy(self, url: str) -> bool:
        """判断是否应该使用代理"""
        return self.is_blocked_domain(url)
    
    def test_proxy_connection(self) -> bool:
        """测试代理连接"""
        try:
            proxy_config = {
                'http': f"http://{self.proxy_host}:{self.proxy_port}",
                'https': f"http://{self.proxy_host}:{self.proxy_port}"
            }
            
            # 测试访问Google
            response = requests.get(
                'https://www.google.com',
                proxies=proxy_config,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("代理连接测试成功")
                return True
            else:
                logger.warning(f"代理连接测试失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"代理连接测试失败: {e}")
            return False
    
    def get_domain_info(self, url: str) -> Dict[str, any]:
        """获取域名信息"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            if ':' in domain:
                domain = domain.split(':')[0]
            
            is_blocked = self.is_blocked_domain(url)
            should_proxy = self.should_use_proxy(url)
            
            return {
                'url': url,
                'domain': domain,
                'is_blocked': is_blocked,
                'should_use_proxy': should_proxy,
                'proxy_config': self.get_proxy_config(url)
            }
            
        except Exception as e:
            logger.error(f"获取域名信息失败: {e}")
            return {}

# 全局实例
proxy_manager = ProxyManager()
