"""
验证码自动识别服务
支持多种识别方法：OCR、在线API等
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import requests
import base64
import json
from typing import Optional, Dict, Any, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CaptchaService:
    def __init__(self):
        self.ocr_methods = []
        self.init_ocr_engines()
    
    def init_ocr_engines(self):
        """初始化OCR引擎"""
        # 1. 尝试初始化Tesseract
        try:
            import pytesseract
            # Windows下可能需要指定tesseract路径
            tesseract_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                "tesseract"  # 如果在PATH中
            ]
            
            for path in tesseract_paths:
                try:
                    if os.path.exists(path) or path == "tesseract":
                        pytesseract.pytesseract.tesseract_cmd = path
                        # 测试是否可用
                        test_img = Image.new('RGB', (100, 30), color='white')
                        pytesseract.image_to_string(test_img)
                        self.ocr_methods.append('tesseract')
                        logger.info(f"Tesseract OCR 初始化成功: {path}")
                        break
                except:
                    continue
        except ImportError:
            logger.warning("pytesseract 未安装")
        except Exception as e:
            logger.warning(f"Tesseract 初始化失败: {e}")
        
        # 2. 尝试初始化EasyOCR
        try:
            import easyocr
            self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
            self.ocr_methods.append('easyocr')
            logger.info("EasyOCR 初始化成功")
        except ImportError:
            logger.warning("easyocr 未安装")
        except Exception as e:
            logger.warning(f"EasyOCR 初始化失败: {e}")
        
        logger.info(f"可用的OCR方法: {self.ocr_methods}")
    
    def preprocess_image(self, image_path: str) -> List[np.ndarray]:
        """
        图像预处理，返回多个处理后的图像用于提高识别率
        """
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                logger.error(f"无法读取图像: {image_path}")
                return []
            
            processed_images = []
            
            # 1. 原图
            processed_images.append(img)
            
            # 2. 灰度化
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            processed_images.append(gray)
            
            # 3. 二值化 - 多种阈值
            for thresh_val in [127, 100, 150, 180]:
                _, binary = cv2.threshold(gray, thresh_val, 255, cv2.THRESH_BINARY)
                processed_images.append(binary)
            
            # 4. 自适应阈值
            adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            processed_images.append(adaptive)
            
            # 5. 降噪
            denoised = cv2.medianBlur(gray, 3)
            processed_images.append(denoised)
            
            # 6. 形态学操作
            kernel = np.ones((2,2), np.uint8)
            morph = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            processed_images.append(morph)
            
            # 7. 锐化
            kernel_sharp = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(gray, -1, kernel_sharp)
            processed_images.append(sharpened)
            
            # 8. 使用PIL进行额外处理
            pil_img = Image.open(image_path)
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(pil_img)
            enhanced = enhancer.enhance(2.0)
            enhanced_array = np.array(enhanced.convert('L'))
            processed_images.append(enhanced_array)
            
            # 增强亮度
            enhancer = ImageEnhance.Brightness(pil_img)
            bright = enhancer.enhance(1.5)
            bright_array = np.array(bright.convert('L'))
            processed_images.append(bright_array)
            
            return processed_images
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return []
    
    def recognize_with_tesseract(self, image_path: str) -> List[str]:
        """使用Tesseract识别验证码"""
        if 'tesseract' not in self.ocr_methods:
            return []
        
        try:
            import pytesseract
            
            results = []
            processed_images = self.preprocess_image(image_path)
            
            # 配置Tesseract参数
            configs = [
                '--psm 8 -c tessedit_char_whitelist=0123456789',  # 只识别数字
                '--psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',  # 数字+大写字母
                '--psm 8 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',  # 数字+字母
                '--psm 7',  # 单行文本
                '--psm 8',  # 单词
                '--psm 13'  # 原始行
            ]
            
            for img_array in processed_images:
                for config in configs:
                    try:
                        if len(img_array.shape) == 3:
                            pil_img = Image.fromarray(cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB))
                        else:
                            pil_img = Image.fromarray(img_array)
                        
                        text = pytesseract.image_to_string(pil_img, config=config).strip()
                        if text and len(text) >= 3 and len(text) <= 6:  # 验证码通常3-6位
                            results.append(text)
                    except Exception as e:
                        continue
            
            # 去重并按长度排序
            unique_results = list(set(results))
            unique_results.sort(key=len)
            
            logger.info(f"Tesseract识别结果: {unique_results}")
            return unique_results
            
        except Exception as e:
            logger.error(f"Tesseract识别失败: {e}")
            return []
    
    def recognize_with_easyocr(self, image_path: str) -> List[str]:
        """使用EasyOCR识别验证码"""
        if 'easyocr' not in self.ocr_methods:
            return []
        
        try:
            results = []
            processed_images = self.preprocess_image(image_path)
            
            for img_array in processed_images[:5]:  # 只处理前5个图像，避免太慢
                try:
                    # EasyOCR需要RGB格式
                    if len(img_array.shape) == 3:
                        rgb_img = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)
                    else:
                        rgb_img = cv2.cvtColor(img_array, cv2.COLOR_GRAY2RGB)
                    
                    ocr_results = self.easyocr_reader.readtext(rgb_img, detail=0)
                    
                    for text in ocr_results:
                        text = text.strip()
                        if text and len(text) >= 3 and len(text) <= 6:
                            results.append(text)
                            
                except Exception as e:
                    continue
            
            # 去重并按长度排序
            unique_results = list(set(results))
            unique_results.sort(key=len)
            
            logger.info(f"EasyOCR识别结果: {unique_results}")
            return unique_results
            
        except Exception as e:
            logger.error(f"EasyOCR识别失败: {e}")
            return []
    
    def recognize_with_online_api(self, image_path: str) -> List[str]:
        """使用在线API识别验证码"""
        try:
            # 这里可以集成各种在线验证码识别服务
            # 例如：超级鹰、若快、打码兔等
            
            # 示例：使用免费的OCR.space API
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode()
            
            # 调用OCR.space API（免费额度有限）
            api_url = "https://api.ocr.space/parse/image"
            payload = {
                'base64Image': f'data:image/png;base64,{image_base64}',
                'language': 'eng',
                'isOverlayRequired': False,
                'OCREngine': 2
            }
            
            # 使用代理（如果需要）
            proxies = self.get_proxy_config()
            
            response = requests.post(api_url, data=payload, proxies=proxies, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('IsErroredOnProcessing') == False:
                    parsed_text = result.get('ParsedResults', [{}])[0].get('ParsedText', '').strip()
                    if parsed_text and len(parsed_text) >= 3 and len(parsed_text) <= 6:
                        logger.info(f"在线API识别结果: {parsed_text}")
                        return [parsed_text]
            
            return []
            
        except Exception as e:
            logger.error(f"在线API识别失败: {e}")
            return []
    
    def get_proxy_config(self) -> Dict[str, str]:
        """获取代理配置"""
        proxy_url = "http://127.0.0.1:7897"
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def recognize_captcha(self, image_path: str) -> Dict[str, Any]:
        """
        综合识别验证码
        返回最可能的结果和所有候选结果
        """
        if not os.path.exists(image_path):
            return {
                'success': False,
                'message': '验证码图片不存在',
                'best_result': None,
                'all_results': []
            }
        
        all_results = []
        
        # 1. 使用Tesseract识别
        tesseract_results = self.recognize_with_tesseract(image_path)
        all_results.extend(tesseract_results)
        
        # 2. 使用EasyOCR识别
        easyocr_results = self.recognize_with_easyocr(image_path)
        all_results.extend(easyocr_results)
        
        # 3. 使用在线API识别（可选）
        try:
            online_results = self.recognize_with_online_api(image_path)
            all_results.extend(online_results)
        except:
            pass
        
        # 去重
        unique_results = list(set(all_results))
        
        if not unique_results:
            return {
                'success': False,
                'message': '未能识别出验证码',
                'best_result': None,
                'all_results': []
            }
        
        # 选择最佳结果
        best_result = self.select_best_result(unique_results)
        
        return {
            'success': True,
            'message': '验证码识别成功',
            'best_result': best_result,
            'all_results': unique_results,
            'confidence': self.calculate_confidence(best_result, unique_results)
        }
    
    def select_best_result(self, results: List[str]) -> str:
        """选择最佳识别结果"""
        if not results:
            return ""
        
        # 统计每个结果出现的频次
        result_count = {}
        for result in results:
            result_count[result] = result_count.get(result, 0) + 1
        
        # 按频次排序，频次相同时按长度排序
        sorted_results = sorted(result_count.items(), key=lambda x: (x[1], len(x[0])), reverse=True)
        
        return sorted_results[0][0]
    
    def calculate_confidence(self, best_result: str, all_results: List[str]) -> float:
        """计算识别置信度"""
        if not best_result or not all_results:
            return 0.0
        
        # 计算最佳结果在所有结果中的出现频率
        count = all_results.count(best_result)
        confidence = count / len(all_results)
        
        return round(confidence, 2)
    
    def save_processed_image(self, image_path: str, output_dir: str = "data/captcha/processed"):
        """保存预处理后的图像用于调试"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            processed_images = self.preprocess_image(image_path)
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            
            for i, img_array in enumerate(processed_images):
                output_path = os.path.join(output_dir, f"{base_name}_processed_{i}.png")
                cv2.imwrite(output_path, img_array)
            
            logger.info(f"预处理图像已保存到: {output_dir}")
            
        except Exception as e:
            logger.error(f"保存预处理图像失败: {e}")

# 全局实例
captcha_service = CaptchaService()
