"""
验证码模型训练器
基于收集的标注数据训练验证码识别模型
"""

import os
import json
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple
import logging
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import joblib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CaptchaTrainer:
    def __init__(self):
        self.labeled_dir = "data/captcha_labeled"
        self.models_dir = "data/models"
        self.training_data_file = "data/captcha_labeled/training_data.json"
        
        # 创建模型目录
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 模型参数
        self.image_size = (50, 22)  # 标准化图片尺寸
        self.feature_dim = self.image_size[0] * self.image_size[1]  # 特征维度
    
    def load_training_data(self) -> List[Dict[str, Any]]:
        """加载训练数据"""
        if not os.path.exists(self.training_data_file):
            logger.error(f"训练数据文件不存在: {self.training_data_file}")
            return []
        
        try:
            with open(self.training_data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"加载了 {len(data)} 个训练样本")
            return data
            
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            return []
    
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """预处理图片，提取特征"""
        try:
            # 读取图片
            img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                logger.error(f"无法读取图片: {image_path}")
                return np.array([])
            
            # 调整尺寸
            img_resized = cv2.resize(img, self.image_size)
            
            # 二值化
            _, img_binary = cv2.threshold(img_resized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 归一化
            img_normalized = img_binary.astype(np.float32) / 255.0
            
            # 展平为一维特征向量
            features = img_normalized.flatten()
            
            return features
            
        except Exception as e:
            logger.error(f"图片预处理失败: {e}")
            return np.array([])
    
    def extract_features_and_labels(self, training_data: List[Dict[str, Any]]) -> Tuple[np.ndarray, List[str]]:
        """提取特征和标签"""
        features_list = []
        labels_list = []
        
        logger.info("开始提取特征...")
        
        for i, sample in enumerate(training_data):
            filepath = sample.get('filepath', '')
            label = sample.get('label', '')
            
            if not os.path.exists(filepath):
                logger.warning(f"文件不存在，跳过: {filepath}")
                continue
            
            if len(label) != 4 or not label.isdigit():
                logger.warning(f"无效标签，跳过: {label}")
                continue
            
            # 提取图片特征
            features = self.preprocess_image(filepath)
            if features.size == 0:
                continue
            
            features_list.append(features)
            labels_list.append(label)
            
            if (i + 1) % 100 == 0:
                logger.info(f"已处理 {i + 1}/{len(training_data)} 个样本")
        
        if not features_list:
            logger.error("没有有效的训练样本")
            return np.array([]), []
        
        features_array = np.array(features_list)
        logger.info(f"特征提取完成，形状: {features_array.shape}")
        
        return features_array, labels_list
    
    def train_digit_models(self, features: np.ndarray, labels: List[str]) -> Dict[str, Any]:
        """训练数字识别模型（每个位置一个模型）"""
        models = {}
        training_info = {
            "training_time": datetime.now().isoformat(),
            "sample_count": len(labels),
            "feature_dim": features.shape[1],
            "models": {}
        }
        
        logger.info("开始训练数字识别模型...")
        
        # 为每个位置训练一个模型
        for position in range(4):  # 4位验证码
            logger.info(f"训练第 {position + 1} 位数字模型...")
            
            # 提取该位置的标签
            position_labels = [label[position] for label in labels]
            
            # 分割训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                features, position_labels, test_size=0.2, random_state=42, stratify=position_labels
            )
            
            # 训练随机森林模型
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            model.fit(X_train, y_train)
            
            # 评估模型
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            logger.info(f"第 {position + 1} 位模型准确率: {accuracy:.3f}")
            
            # 保存模型
            model_filename = f"digit_model_position_{position}.joblib"
            model_path = os.path.join(self.models_dir, model_filename)
            joblib.dump(model, model_path)
            
            models[f"position_{position}"] = {
                "model": model,
                "model_path": model_path,
                "accuracy": accuracy,
                "train_samples": len(X_train),
                "test_samples": len(X_test)
            }
            
            training_info["models"][f"position_{position}"] = {
                "model_file": model_filename,
                "accuracy": accuracy,
                "train_samples": len(X_train),
                "test_samples": len(X_test)
            }
        
        # 保存训练信息
        info_file = os.path.join(self.models_dir, "training_info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(training_info, f, ensure_ascii=False, indent=2)
        
        logger.info("模型训练完成！")
        return models
    
    def train_whole_captcha_model(self, features: np.ndarray, labels: List[str]) -> Dict[str, Any]:
        """训练整体验证码识别模型"""
        logger.info("训练整体验证码识别模型...")
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=0.2, random_state=42
        )
        
        # 训练随机森林模型
        model = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            random_state=42,
            n_jobs=-1
        )
        
        model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"整体模型准确率: {accuracy:.3f}")
        
        # 保存模型
        model_path = os.path.join(self.models_dir, "whole_captcha_model.joblib")
        joblib.dump(model, model_path)
        
        # 生成详细报告
        report = classification_report(y_test, y_pred, output_dict=True)
        
        model_info = {
            "model_path": model_path,
            "accuracy": accuracy,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "classification_report": report
        }
        
        return model_info
    
    def train_models(self) -> Dict[str, Any]:
        """训练所有模型"""
        logger.info("开始训练验证码识别模型...")
        
        # 加载训练数据
        training_data = self.load_training_data()
        if not training_data:
            return {"status": "error", "message": "没有训练数据"}
        
        # 提取特征和标签
        features, labels = self.extract_features_and_labels(training_data)
        if features.size == 0:
            return {"status": "error", "message": "特征提取失败"}
        
        training_result = {
            "status": "success",
            "training_time": datetime.now().isoformat(),
            "sample_count": len(labels),
            "feature_dim": features.shape[1]
        }
        
        try:
            # 训练分位置模型
            logger.info("训练分位置数字模型...")
            digit_models = self.train_digit_models(features, labels)
            training_result["digit_models"] = {
                pos: info for pos, info in digit_models.items() 
                if pos != "model"  # 排除模型对象，只保留信息
            }
            
            # 训练整体模型
            logger.info("训练整体验证码模型...")
            whole_model = self.train_whole_captcha_model(features, labels)
            training_result["whole_model"] = whole_model
            
            # 计算平均准确率
            digit_accuracies = [info["accuracy"] for info in digit_models.values()]
            training_result["average_digit_accuracy"] = np.mean(digit_accuracies)
            training_result["whole_model_accuracy"] = whole_model["accuracy"]
            
            logger.info(f"训练完成！")
            logger.info(f"分位置模型平均准确率: {training_result['average_digit_accuracy']:.3f}")
            logger.info(f"整体模型准确率: {training_result['whole_model_accuracy']:.3f}")
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            training_result["status"] = "error"
            training_result["error"] = str(e)
        
        return training_result
    
    def load_trained_models(self) -> Dict[str, Any]:
        """加载已训练的模型"""
        models = {}
        
        try:
            # 加载分位置模型
            for position in range(4):
                model_path = os.path.join(self.models_dir, f"digit_model_position_{position}.joblib")
                if os.path.exists(model_path):
                    model = joblib.load(model_path)
                    models[f"position_{position}"] = model
                else:
                    logger.warning(f"模型文件不存在: {model_path}")
            
            # 加载整体模型
            whole_model_path = os.path.join(self.models_dir, "whole_captcha_model.joblib")
            if os.path.exists(whole_model_path):
                models["whole_model"] = joblib.load(whole_model_path)
            else:
                logger.warning(f"整体模型文件不存在: {whole_model_path}")
            
            # 加载训练信息
            info_file = os.path.join(self.models_dir, "training_info.json")
            if os.path.exists(info_file):
                with open(info_file, 'r', encoding='utf-8') as f:
                    models["training_info"] = json.load(f)
            
            logger.info(f"加载了 {len([k for k in models.keys() if 'position_' in k])} 个分位置模型")
            if "whole_model" in models:
                logger.info("加载了整体模型")
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
        
        return models
    
    def predict_captcha(self, image_path: str, models: Dict[str, Any] = None) -> Dict[str, Any]:
        """使用训练好的模型预测验证码"""
        if models is None:
            models = self.load_trained_models()
        
        if not models:
            return {"status": "error", "message": "没有可用的模型"}
        
        try:
            # 预处理图片
            features = self.preprocess_image(image_path)
            if features.size == 0:
                return {"status": "error", "message": "图片预处理失败"}
            
            features = features.reshape(1, -1)  # 转换为模型输入格式
            
            predictions = {}
            
            # 使用分位置模型预测
            if any(f"position_{i}" in models for i in range(4)):
                digit_predictions = []
                digit_confidences = []
                
                for position in range(4):
                    model_key = f"position_{position}"
                    if model_key in models:
                        model = models[model_key]
                        pred = model.predict(features)[0]
                        prob = model.predict_proba(features)[0]
                        confidence = np.max(prob)
                        
                        digit_predictions.append(pred)
                        digit_confidences.append(confidence)
                    else:
                        digit_predictions.append("0")
                        digit_confidences.append(0.0)
                
                predictions["digit_model"] = {
                    "prediction": "".join(digit_predictions),
                    "confidence": np.mean(digit_confidences),
                    "digit_confidences": digit_confidences
                }
            
            # 使用整体模型预测
            if "whole_model" in models:
                model = models["whole_model"]
                pred = model.predict(features)[0]
                prob = model.predict_proba(features)[0]
                confidence = np.max(prob)
                
                predictions["whole_model"] = {
                    "prediction": pred,
                    "confidence": confidence
                }
            
            # 选择最佳预测
            best_prediction = None
            best_confidence = 0.0
            
            for model_name, result in predictions.items():
                if result["confidence"] > best_confidence:
                    best_prediction = result["prediction"]
                    best_confidence = result["confidence"]
            
            return {
                "status": "success",
                "best_prediction": best_prediction,
                "best_confidence": best_confidence,
                "all_predictions": predictions
            }
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {"status": "error", "message": str(e)}

# 全局实例
captcha_trainer = CaptchaTrainer()
