"""
简化版验证码识别服务 - 仅使用Tesseract OCR
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import requests
import base64
import json
from typing import Optional, Dict, Any, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleCaptchaService:
    def __init__(self):
        self.ocr_methods = []
        self.init_tesseract()
    
    def init_tesseract(self):
        """初始化Tesseract OCR"""
        try:
            import pytesseract
            # Windows下可能需要指定tesseract路径
            tesseract_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                "tesseract"  # 如果在PATH中
            ]
            
            for path in tesseract_paths:
                try:
                    if os.path.exists(path) or path == "tesseract":
                        pytesseract.pytesseract.tesseract_cmd = path
                        # 测试是否可用
                        test_img = Image.new('RGB', (100, 30), color='white')
                        pytesseract.image_to_string(test_img)
                        self.ocr_methods.append('tesseract')
                        logger.info(f"Tesseract OCR 初始化成功: {path}")
                        break
                except:
                    continue
        except ImportError:
            logger.warning("pytesseract 未安装")
        except Exception as e:
            logger.warning(f"Tesseract 初始化失败: {e}")
        
        logger.info(f"可用的OCR方法: {self.ocr_methods}")
    
    def preprocess_image(self, image_path: str) -> List[np.ndarray]:
        """图像预处理"""
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                logger.error(f"无法读取图像: {image_path}")
                return []
            
            processed_images = []
            
            # 1. 原图
            processed_images.append(img)
            
            # 2. 灰度化
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            processed_images.append(gray)
            
            # 3. 二值化 - 多种阈值
            for thresh_val in [127, 100, 150, 180]:
                _, binary = cv2.threshold(gray, thresh_val, 255, cv2.THRESH_BINARY)
                processed_images.append(binary)
            
            # 4. 自适应阈值
            adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            processed_images.append(adaptive)
            
            # 5. 降噪
            denoised = cv2.medianBlur(gray, 3)
            processed_images.append(denoised)
            
            # 6. 使用PIL进行额外处理
            pil_img = Image.open(image_path)
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(pil_img)
            enhanced = enhancer.enhance(2.0)
            enhanced_array = np.array(enhanced.convert('L'))
            processed_images.append(enhanced_array)
            
            return processed_images
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return []
    
    def recognize_with_tesseract(self, image_path: str) -> List[str]:
        """使用Tesseract识别验证码"""
        if 'tesseract' not in self.ocr_methods:
            return []
        
        try:
            import pytesseract
            
            results = []
            processed_images = self.preprocess_image(image_path)
            
            # 配置Tesseract参数
            configs = [
                '--psm 8 -c tessedit_char_whitelist=0123456789',  # 只识别数字
                '--psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',  # 数字+大写字母
                '--psm 8 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',  # 数字+字母
                '--psm 7',  # 单行文本
                '--psm 8',  # 单词
                '--psm 13'  # 原始行
            ]
            
            for img_array in processed_images:
                for config in configs:
                    try:
                        if len(img_array.shape) == 3:
                            pil_img = Image.fromarray(cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB))
                        else:
                            pil_img = Image.fromarray(img_array)
                        
                        text = pytesseract.image_to_string(pil_img, config=config).strip()
                        # 清理识别结果
                        text = ''.join(c for c in text if c.isalnum())
                        if text and len(text) >= 3 and len(text) <= 6:  # 验证码通常3-6位
                            results.append(text)
                    except Exception as e:
                        continue
            
            # 去重并按长度排序
            unique_results = list(set(results))
            unique_results.sort(key=len)
            
            logger.info(f"Tesseract识别结果: {unique_results}")
            return unique_results
            
        except Exception as e:
            logger.error(f"Tesseract识别失败: {e}")
            return []
    
    def get_proxy_config(self) -> Dict[str, str]:
        """获取代理配置"""
        proxy_url = "http://127.0.0.1:7897"
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def recognize_captcha(self, image_path: str) -> Dict[str, Any]:
        """识别验证码"""
        if not os.path.exists(image_path):
            return {
                'success': False,
                'message': '验证码图片不存在',
                'best_result': None,
                'all_results': []
            }
        
        all_results = []
        
        # 使用Tesseract识别
        tesseract_results = self.recognize_with_tesseract(image_path)
        all_results.extend(tesseract_results)
        
        # 去重
        unique_results = list(set(all_results))
        
        if not unique_results:
            return {
                'success': False,
                'message': '未能识别出验证码',
                'best_result': None,
                'all_results': []
            }
        
        # 选择最佳结果
        best_result = self.select_best_result(unique_results)
        
        return {
            'success': True,
            'message': '验证码识别成功',
            'best_result': best_result,
            'all_results': unique_results,
            'confidence': self.calculate_confidence(best_result, unique_results)
        }
    
    def select_best_result(self, results: List[str]) -> str:
        """选择最佳识别结果"""
        if not results:
            return ""
        
        # 统计每个结果出现的频次
        result_count = {}
        for result in results:
            result_count[result] = result_count.get(result, 0) + 1
        
        # 按频次排序，频次相同时按长度排序
        sorted_results = sorted(result_count.items(), key=lambda x: (x[1], len(x[0])), reverse=True)
        
        return sorted_results[0][0]
    
    def calculate_confidence(self, best_result: str, all_results: List[str]) -> float:
        """计算识别置信度"""
        if not best_result or not all_results:
            return 0.0
        
        # 计算最佳结果在所有结果中的出现频率
        count = all_results.count(best_result)
        confidence = count / len(all_results)
        
        return round(confidence, 2)

# 全局实例
simple_captcha_service = SimpleCaptchaService()
