"""
验证码批量下载和数据收集服务
用于收集大量验证码样本进行模型训练
"""

import os
import time
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import requests
import cv2
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CaptchaCollector:
    def __init__(self):
        self.driver = None
        self.base_url = "https://eir.cmclink.com"
        self.collection_dir = "data/captcha_collection"
        self.metadata_file = "data/captcha_collection/metadata.json"

        # 创建收集目录
        os.makedirs(self.collection_dir, exist_ok=True)

        # 初始化元数据
        self.metadata = self.load_metadata()

    def load_metadata(self) -> Dict[str, Any]:
        """加载元数据"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass

        return {
            "total_collected": 0,
            "collection_sessions": [],
            "captcha_files": [],
            "statistics": {
                "success_count": 0,
                "failed_count": 0,
                "duplicate_count": 0
            }
        }

    def save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")

    def init_driver(self) -> bool:
        """初始化浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # EIR是国内网站，不需要代理
            logger.info("EIR网站无需代理，直连访问")

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置超时时间
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)

            logger.info("浏览器驱动初始化成功")
            return True

        except Exception as e:
            logger.error(f"浏览器驱动初始化失败: {e}")
            return False

    def download_single_captcha(self, session_id: str, index: int) -> Optional[Dict[str, Any]]:
        """下载单个验证码"""
        try:
            # 访问登录页面
            self.driver.get(self.base_url)
            time.sleep(2)

            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.NAME, "txtUserName"))
            )

            # 查找验证码图片元素
            captcha_img = self.driver.find_element(By.ID, "imgValidateCode")
            captcha_src = captcha_img.get_attribute("src")

            if not captcha_src:
                logger.warning(f"第 {index} 个验证码: 无法获取验证码图片URL")
                return None

            # 下载验证码图片
            timestamp = int(time.time() * 1000)
            filename = f"captcha_{session_id}_{index:04d}_{timestamp}.png"
            filepath = os.path.join(self.collection_dir, filename)

            # 使用requests下载图片
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': self.base_url
            }

            # 获取cookies
            cookies = {}
            for cookie in self.driver.get_cookies():
                cookies[cookie['name']] = cookie['value']

            # EIR是国内网站，不需要代理
            proxies = {}

            response = requests.get(captcha_src, headers=headers, cookies=cookies, proxies=proxies, timeout=10)

            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    f.write(response.content)

                # 验证图片是否有效
                if self.validate_captcha_image(filepath):
                    # 分析图片特征
                    features = self.analyze_captcha_features(filepath)

                    captcha_info = {
                        "filename": filename,
                        "filepath": filepath,
                        "session_id": session_id,
                        "index": index,
                        "timestamp": timestamp,
                        "download_time": datetime.now().isoformat(),
                        "file_size": os.path.getsize(filepath),
                        "features": features,
                        "labeled": False,
                        "label": None
                    }

                    logger.info(f"✅ 第 {index} 个验证码下载成功: {filename}")
                    return captcha_info
                else:
                    # 删除无效图片
                    if os.path.exists(filepath):
                        os.remove(filepath)
                    logger.warning(f"第 {index} 个验证码: 图片无效，已删除")
                    return None
            else:
                logger.warning(f"第 {index} 个验证码: 下载失败，状态码 {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"第 {index} 个验证码下载异常: {e}")
            return None

    def validate_captcha_image(self, filepath: str) -> bool:
        """验证验证码图片是否有效"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(filepath)
            if file_size < 500:  # 太小的文件可能无效
                return False

            # 使用OpenCV读取图片
            img = cv2.imread(filepath)
            if img is None:
                return False

            # 检查图片尺寸
            height, width = img.shape[:2]
            if height < 15 or width < 30:  # 尺寸太小
                return False

            # 检查是否为空白图片
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            if np.std(gray) < 10:  # 标准差太小，可能是空白图片
                return False

            return True

        except Exception as e:
            logger.error(f"验证图片失败: {e}")
            return False

    def analyze_captcha_features(self, filepath: str) -> Dict[str, Any]:
        """分析验证码图片特征"""
        try:
            img = cv2.imread(filepath)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            height, width = gray.shape

            # 基本特征
            features = {
                "width": width,
                "height": height,
                "aspect_ratio": width / height,
                "file_size": os.path.getsize(filepath)
            }

            # 像素统计
            features["mean_intensity"] = float(np.mean(gray))
            features["std_intensity"] = float(np.std(gray))
            features["min_intensity"] = int(np.min(gray))
            features["max_intensity"] = int(np.max(gray))

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            features["edge_density"] = float(np.sum(edges) / (width * height))

            # 二值化分析
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            features["black_pixel_ratio"] = float(np.sum(binary == 0) / (width * height))

            return features

        except Exception as e:
            logger.error(f"分析图片特征失败: {e}")
            return {}

    def collect_captchas(self, target_count: int = 1000, batch_size: int = 50) -> Dict[str, Any]:
        """批量收集验证码"""
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        session_info = {
            "session_id": session_id,
            "start_time": datetime.now().isoformat(),
            "target_count": target_count,
            "batch_size": batch_size,
            "collected_count": 0,
            "success_count": 0,
            "failed_count": 0,
            "status": "running"
        }

        logger.info(f"开始收集验证码，目标数量: {target_count}")

        if not self.init_driver():
            session_info["status"] = "failed"
            session_info["error"] = "浏览器初始化失败"
            return session_info

        try:
            collected_files = []

            for i in range(target_count):
                try:
                    # 每批次后短暂休息
                    if i > 0 and i % batch_size == 0:
                        logger.info(f"已收集 {i} 个验证码，休息 5 秒...")
                        time.sleep(5)

                    captcha_info = self.download_single_captcha(session_id, i + 1)

                    if captcha_info:
                        collected_files.append(captcha_info)
                        session_info["success_count"] += 1
                        self.metadata["statistics"]["success_count"] += 1
                    else:
                        session_info["failed_count"] += 1
                        self.metadata["statistics"]["failed_count"] += 1

                    session_info["collected_count"] = i + 1

                    # 每10个验证码保存一次元数据
                    if (i + 1) % 10 == 0:
                        self.metadata["captcha_files"].extend(collected_files[-10:])
                        self.metadata["total_collected"] = len(self.metadata["captcha_files"])
                        self.save_metadata()
                        logger.info(f"进度: {i + 1}/{target_count} ({(i + 1)/target_count*100:.1f}%)")

                    # 随机延迟，避免被检测
                    time.sleep(np.random.uniform(0.5, 2.0))

                except KeyboardInterrupt:
                    logger.info("用户中断收集")
                    break
                except Exception as e:
                    logger.error(f"收集第 {i + 1} 个验证码时发生错误: {e}")
                    session_info["failed_count"] += 1
                    continue

            # 更新会话信息
            session_info["end_time"] = datetime.now().isoformat()
            session_info["status"] = "completed"
            session_info["collected_files"] = collected_files

            # 更新元数据
            self.metadata["captcha_files"].extend(collected_files)
            self.metadata["total_collected"] = len(self.metadata["captcha_files"])
            self.metadata["collection_sessions"].append(session_info)
            self.save_metadata()

            logger.info(f"验证码收集完成！成功: {session_info['success_count']}, 失败: {session_info['failed_count']}")

        except Exception as e:
            session_info["status"] = "error"
            session_info["error"] = str(e)
            logger.error(f"收集过程发生错误: {e}")

        finally:
            if self.driver:
                self.driver.quit()

        return session_info

    def get_collection_statistics(self) -> Dict[str, Any]:
        """获取收集统计信息"""
        stats = {
            "total_collected": self.metadata["total_collected"],
            "statistics": self.metadata["statistics"],
            "sessions_count": len(self.metadata["collection_sessions"]),
            "collection_dir": self.collection_dir
        }

        # 分析文件大小分布
        if self.metadata["captcha_files"]:
            file_sizes = [f.get("file_size", 0) for f in self.metadata["captcha_files"]]
            stats["file_size_stats"] = {
                "min": min(file_sizes),
                "max": max(file_sizes),
                "mean": np.mean(file_sizes),
                "std": np.std(file_sizes)
            }

        # 分析图片特征分布
        if self.metadata["captcha_files"]:
            widths = [f.get("features", {}).get("width", 0) for f in self.metadata["captcha_files"]]
            heights = [f.get("features", {}).get("height", 0) for f in self.metadata["captcha_files"]]

            if widths and heights:
                stats["image_stats"] = {
                    "width_range": [min(widths), max(widths)],
                    "height_range": [min(heights), max(heights)],
                    "common_size": f"{max(set(widths), key=widths.count)}x{max(set(heights), key=heights.count)}"
                }

        return stats

    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()

# 全局实例
captcha_collector = CaptchaCollector()
