import sqlite3
import json
from datetime import datetime
from typing import Optional, Dict, Any
import os

class DatabaseManager:
    def __init__(self, db_path: str = "logistics.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建登录会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS login_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform TEXT NOT NULL,
                    username TEXT NOT NULL,
                    cookies TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # 创建订单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    so_number TEXT NOT NULL,
                    shipping_company TEXT,
                    container_type TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建操作日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS operation_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operation_type TEXT NOT NULL,
                    platform TEXT,
                    details TEXT,
                    status TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def save_cookies(self, platform: str, username: str, cookies: Dict[str, Any]) -> bool:
        """保存登录cookies"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 先将该平台的其他会话设为非活跃
                cursor.execute('''
                    UPDATE login_sessions 
                    SET is_active = 0 
                    WHERE platform = ? AND username = ?
                ''', (platform, username))
                
                # 插入新的会话
                cursor.execute('''
                    INSERT INTO login_sessions (platform, username, cookies, updated_at)
                    VALUES (?, ?, ?, ?)
                ''', (platform, username, json.dumps(cookies), datetime.now()))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"保存cookies失败: {e}")
            return False
    
    def get_cookies(self, platform: str, username: str) -> Optional[Dict[str, Any]]:
        """获取登录cookies"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT cookies FROM login_sessions 
                    WHERE platform = ? AND username = ? AND is_active = 1
                    ORDER BY updated_at DESC LIMIT 1
                ''', (platform, username))
                
                result = cursor.fetchone()
                if result:
                    return json.loads(result[0])
                return None
        except Exception as e:
            print(f"获取cookies失败: {e}")
            return None
    
    def log_operation(self, operation_type: str, platform: str = None, 
                     details: str = None, status: str = "success", 
                     error_message: str = None):
        """记录操作日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO operation_logs 
                    (operation_type, platform, details, status, error_message)
                    VALUES (?, ?, ?, ?, ?)
                ''', (operation_type, platform, details, status, error_message))
                conn.commit()
        except Exception as e:
            print(f"记录日志失败: {e}")
    
    def save_order(self, so_number: str, shipping_company: str = None, 
                   container_type: str = None) -> int:
        """保存订单信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO orders (so_number, shipping_company, container_type)
                    VALUES (?, ?, ?)
                ''', (so_number, shipping_company, container_type))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"保存订单失败: {e}")
            return 0
    
    def update_order_status(self, order_id: int, status: str):
        """更新订单状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE orders 
                    SET status = ?, updated_at = ?
                    WHERE id = ?
                ''', (status, datetime.now(), order_id))
                conn.commit()
        except Exception as e:
            print(f"更新订单状态失败: {e}")
