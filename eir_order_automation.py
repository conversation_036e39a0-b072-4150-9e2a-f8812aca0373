#!/usr/bin/env python3
"""
EIR打单自动化功能
实现【业务办理】->【出口EIR缴费并打单】的自动化流程
"""

import sys
import os
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class EIROrderAutomation:
    def __init__(self):
        self.driver = None
        self.base_url = "https://eir.cmclink.com"
        self.username = "Boshanwl"
        self.password = "BOS,./"

    def init_driver(self):
        """初始化Chrome驱动"""
        try:
            chrome_options = Options()

            # 调试模式 - 可以看到浏览器操作
            # chrome_options.add_argument("--headless")  # 注释掉无头模式

            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            print("✅ Chrome浏览器启动成功")
            return True

        except Exception as e:
            print(f"❌ Chrome浏览器启动失败: {e}")
            return False

    def login_to_eir(self):
        """登录到EIR系统"""
        try:
            print("🔐 开始登录EIR系统...")

            # 访问登录页面
            self.driver.get(self.base_url)
            time.sleep(3)

            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.NAME, "tttteeeexxxxtttt1"))
            )

            # 填写用户名
            username_input = self.driver.find_element(By.NAME, "tttteeeexxxxtttt1")
            username_input.clear()
            username_input.send_keys(self.username)

            # 填写密码
            password_input = self.driver.find_element(By.NAME, "tttteeeexxxxtttt2")
            password_input.clear()
            password_input.send_keys(self.password)

            # 处理验证码
            print("🤖 识别验证码...")
            captcha_code = self.handle_captcha()

            if captcha_code:
                # 填写验证码
                captcha_input = self.driver.find_element(By.NAME, "TextBoxValidateCode")
                captcha_input.clear()
                captcha_input.send_keys(captcha_code)

                # 点击登录
                login_button = self.driver.find_element(By.NAME, "btnLogin")
                login_button.click()

                # 等待登录结果
                time.sleep(5)

                # 检查是否登录成功
                current_url = self.driver.current_url
                if "default.aspx" in current_url:
                    print("✅ 登录成功")
                    return True
                else:
                    print("❌ 登录失败")
                    return False
            else:
                print("❌ 验证码识别失败")
                return False

        except Exception as e:
            print(f"❌ 登录过程异常: {e}")
            return False

    def handle_captcha(self):
        """处理验证码识别"""
        try:
            from backend.app.services.captcha_service_ml import ml_captcha_service

            # 下载验证码
            captcha_img = self.driver.find_element(By.XPATH, "//img[contains(@src, 'ValidateCode')]")
            captcha_src = captcha_img.get_attribute("src")

            import requests
            response = requests.get(captcha_src, cookies={cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()})

            if response.status_code == 200:
                captcha_path = "temp_captcha.png"
                with open(captcha_path, 'wb') as f:
                    f.write(response.content)

                # 使用机器学习识别
                result = ml_captcha_service.recognize_captcha(captcha_path)

                if result.get("success"):
                    prediction = result.get("best_result")
                    confidence = result.get("confidence", 0)
                    print(f"✅ 验证码识别: {prediction} (置信度: {confidence:.3f})")

                    # 清理临时文件
                    os.remove(captcha_path)

                    return prediction
                else:
                    print("❌ 验证码识别失败")
                    return None
            else:
                print("❌ 验证码下载失败")
                return None

        except Exception as e:
            print(f"❌ 验证码处理异常: {e}")
            return None

    def navigate_to_order_page(self):
        """导航到打单页面"""
        try:
            print("🧭 导航到【业务办理】->【出口EIR缴费并打单】...")

            # 等待页面加载完成
            time.sleep(3)

            # 查找业务办理菜单
            print("1. 查找【业务办理】菜单...")

            # 尝试多种可能的选择器
            business_menu_selectors = [
                "//a[contains(text(), '业务办理')]",
                "//td[contains(text(), '业务办理')]",
                "//span[contains(text(), '业务办理')]",
                "//div[contains(text(), '业务办理')]"
            ]

            business_menu = None
            for selector in business_menu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        business_menu = elements[0]
                        print(f"✅ 找到业务办理菜单: {selector}")
                        break
                except:
                    continue

            if not business_menu:
                print("❌ 未找到【业务办理】菜单")
                return False

            # 点击业务办理
            business_menu.click()
            time.sleep(2)

            # 查找出口EIR缴费并打单
            print("2. 查找【出口EIR缴费并打单】...")

            export_eir_selectors = [
                "//a[contains(text(), '出口EIR缴费并打单')]",
                "//a[contains(text(), '出口EIR')]",
                "//a[contains(text(), '缴费并打单')]",
                "//td[contains(text(), '出口EIR缴费并打单')]"
            ]

            export_eir_menu = None
            for selector in export_eir_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        export_eir_menu = elements[0]
                        print(f"✅ 找到出口EIR缴费并打单: {selector}")
                        break
                except:
                    continue

            if not export_eir_menu:
                print("❌ 未找到【出口EIR缴费并打单】菜单")

                # 显示所有可用的链接和菜单项
                print("🔍 分析所有可用的菜单项...")

                # 查找所有链接
                all_links = self.driver.find_elements(By.TAG_NAME, "a")
                print(f"找到 {len(all_links)} 个链接:")

                for i, link in enumerate(all_links[:20]):  # 显示前20个
                    try:
                        text = link.text.strip()
                        href = link.get_attribute("href")
                        if text and len(text) < 50:  # 过滤太长的文本
                            print(f"  {i+1}. {text} -> {href}")
                    except:
                        pass

                # 查找所有包含“EIR”的元素
                eir_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'EIR')]")
                if eir_elements:
                    print(f"\n找到 {len(eir_elements)} 个包含'EIR'的元素:")
                    for i, elem in enumerate(eir_elements[:10]):
                        try:
                            text = elem.text.strip()
                            tag = elem.tag_name
                            if text:
                                print(f"  {i+1}. <{tag}> {text}")
                        except:
                            pass

                # 查找所有包含“打单”的元素
                order_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '打单')]")
                if order_elements:
                    print(f"\n找到 {len(order_elements)} 个包含'打单'的元素:")
                    for i, elem in enumerate(order_elements[:10]):
                        try:
                            text = elem.text.strip()
                            tag = elem.tag_name
                            if text:
                                print(f"  {i+1}. <{tag}> {text}")
                        except:
                            pass

                return False

            # 点击出口EIR缴费并打单
            export_eir_menu.click()
            time.sleep(3)

            print("✅ 成功导航到打单页面")
            return True

        except Exception as e:
            print(f"❌ 导航异常: {e}")
            return False

    def analyze_order_page(self):
        """分析打单页面结构"""
        try:
            print("🔍 分析打单页面结构...")

            current_url = self.driver.current_url
            page_title = self.driver.title

            print(f"📄 当前页面:")
            print(f"   URL: {current_url}")
            print(f"   标题: {page_title}")

            # 查找所有表单元素
            print("\n📋 查找表单元素:")

            # 查找Carrier选择框
            print("1. 查找Carrier(船公司)选择框...")
            carrier_selectors = [
                "//select[contains(@name, 'carrier') or contains(@name, 'Carrier')]",
                "//select[contains(@id, 'carrier') or contains(@id, 'Carrier')]",
                "//select[contains(@class, 'carrier')]",
                "//select[preceding-sibling::*[contains(text(), '船公司')] or preceding-sibling::*[contains(text(), 'Carrier')]]"
            ]

            carrier_element = None
            for selector in carrier_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        carrier_element = elements[0]
                        print(f"   ✅ 找到Carrier选择框: {selector}")

                        # 获取选项
                        select = Select(carrier_element)
                        options = [option.text for option in select.options]
                        print(f"   📋 可选船公司: {options[:10]}...")  # 显示前10个
                        break
                except:
                    continue

            if not carrier_element:
                print("   ❌ 未找到Carrier选择框")

            # 查找订舱号输入框
            print("\n2. 查找订舱号(S/O)输入框...")
            so_selectors = [
                "//input[contains(@name, 'so') or contains(@name, 'SO')]",
                "//input[contains(@id, 'so') or contains(@id, 'SO')]",
                "//input[contains(@placeholder, '订舱号') or contains(@placeholder, 'S/O')]",
                "//input[preceding-sibling::*[contains(text(), '订舱号')] or preceding-sibling::*[contains(text(), 'S/O')]]"
            ]

            so_element = None
            for selector in so_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        so_element = elements[0]
                        print(f"   ✅ 找到订舱号输入框: {selector}")
                        break
                except:
                    continue

            if not so_element:
                print("   ❌ 未找到订舱号输入框")

            # 查找查询按钮
            print("\n3. 查找【查询】按钮...")
            query_selectors = [
                "//input[@type='button' and contains(@value, '查询')]",
                "//button[contains(text(), '查询')]",
                "//input[@type='submit' and contains(@value, '查询')]",
                "//a[contains(text(), '查询')]"
            ]

            query_element = None
            for selector in query_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        query_element = elements[0]
                        print(f"   ✅ 找到查询按钮: {selector}")
                        break
                except:
                    continue

            if not query_element:
                print("   ❌ 未找到查询按钮")

            # 查找所有input和select元素
            print("\n📋 所有表单元素分析:")

            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            print(f"   找到 {len(inputs)} 个input元素:")

            for i, input_elem in enumerate(inputs[:15]):  # 显示前15个
                try:
                    input_type = input_elem.get_attribute("type")
                    input_name = input_elem.get_attribute("name")
                    input_id = input_elem.get_attribute("id")
                    input_value = input_elem.get_attribute("value")
                    input_placeholder = input_elem.get_attribute("placeholder")

                    print(f"     {i+1}. type={input_type}, name={input_name}, id={input_id}, value={input_value}, placeholder={input_placeholder}")
                except:
                    print(f"     {i+1}. 无法获取属性")

            selects = self.driver.find_elements(By.TAG_NAME, "select")
            print(f"\n   找到 {len(selects)} 个select元素:")

            for i, select_elem in enumerate(selects):
                try:
                    select_name = select_elem.get_attribute("name")
                    select_id = select_elem.get_attribute("id")

                    select = Select(select_elem)
                    options = [option.text for option in select.options[:5]]  # 前5个选项

                    print(f"     {i+1}. name={select_name}, id={select_id}, options={options}...")
                except:
                    print(f"     {i+1}. 无法获取属性")

            return True

        except Exception as e:
            print(f"❌ 页面分析异常: {e}")
            return False

    def test_order_process(self, carrier="", so_number=""):
        """测试打单流程"""
        try:
            print(f"🧪 测试打单流程...")
            print(f"   船公司: {carrier}")
            print(f"   订舱号: {so_number}")

            if not carrier or not so_number:
                print("⚠️ 需要提供船公司和订舱号进行测试")
                return False

            # 1. 选择船公司
            print("1. 选择船公司...")
            try:
                carrier_select = self.driver.find_element(By.XPATH, "//select[contains(@name, 'carrier') or contains(@name, 'Carrier')]")
                select = Select(carrier_select)

                # 尝试按文本选择
                try:
                    select.select_by_visible_text(carrier)
                    print(f"   ✅ 选择船公司: {carrier}")
                except:
                    # 如果精确匹配失败，尝试模糊匹配
                    for option in select.options:
                        if carrier.lower() in option.text.lower():
                            select.select_by_visible_text(option.text)
                            print(f"   ✅ 选择船公司: {option.text}")
                            break
                    else:
                        print(f"   ❌ 未找到船公司: {carrier}")
                        return False
            except Exception as e:
                print(f"   ❌ 选择船公司失败: {e}")
                return False

            # 2. 输入订舱号
            print("2. 输入订舱号...")
            try:
                so_input = self.driver.find_element(By.XPATH, "//input[contains(@name, 'so') or contains(@name, 'SO')]")
                so_input.clear()
                so_input.send_keys(so_number)
                print(f"   ✅ 输入订舱号: {so_number}")
            except Exception as e:
                print(f"   ❌ 输入订舱号失败: {e}")
                return False

            # 3. 点击查询
            print("3. 点击查询...")
            try:
                query_button = self.driver.find_element(By.XPATH, "//input[@type='button' and contains(@value, '查询')]")
                query_button.click()
                print("   ✅ 点击查询按钮")

                # 等待查询结果
                time.sleep(5)

                # 检查是否有查询结果
                print("4. 检查查询结果...")

                # 查找可能的结果区域
                result_indicators = [
                    "//table[contains(@class, 'result')]",
                    "//div[contains(@class, 'result')]",
                    "//td[contains(text(), '单据地点')]",
                    "//td[contains(text(), '提柜地点')]"
                ]

                has_result = False
                for indicator in result_indicators:
                    try:
                        elements = self.driver.find_elements(By.XPATH, indicator)
                        if elements:
                            print(f"   ✅ 找到查询结果: {indicator}")
                            has_result = True
                            break
                    except:
                        continue

                if has_result:
                    print("   ✅ 查询成功，找到结果")

                    # 查找提柜地点选择
                    print("5. 查找提柜地点选择...")
                    try:
                        location_selects = self.driver.find_elements(By.XPATH, "//select[contains(@name, '地点') or contains(@name, 'location')]")
                        if location_selects:
                            print(f"   ✅ 找到 {len(location_selects)} 个地点选择框")

                            for i, select_elem in enumerate(location_selects):
                                select = Select(select_elem)
                                options = [option.text for option in select.options]
                                print(f"     选择框 {i+1}: {options}")

                                # 优先选择码头
                                for option in select.options:
                                    if "码头" in option.text and "堆场" not in option.text:
                                        select.select_by_visible_text(option.text)
                                        print(f"     ✅ 选择码头: {option.text}")
                                        break
                        else:
                            print("   ⚠️ 未找到提柜地点选择框")
                    except Exception as e:
                        print(f"   ⚠️ 处理提柜地点选择异常: {e}")

                    # 查找缴费按钮
                    print("6. 查找【缴费】按钮...")
                    try:
                        pay_buttons = self.driver.find_elements(By.XPATH, "//input[contains(@value, '缴费')] | //button[contains(text(), '缴费')]")
                        if pay_buttons:
                            print(f"   ✅ 找到缴费按钮")

                            # 询问是否继续缴费
                            choice = input("   是否点击缴费按钮？(y/n): ").strip().lower()
                            if choice == 'y':
                                pay_buttons[0].click()
                                print("   ✅ 点击缴费按钮")

                                time.sleep(3)

                                # 查找确认支付按钮
                                print("7. 查找【确认支付】按钮...")
                                confirm_buttons = self.driver.find_elements(By.XPATH, "//input[contains(@value, '确认支付')] | //button[contains(text(), '确认支付')]")
                                if confirm_buttons:
                                    print("   ✅ 找到确认支付按钮")
                                    print("   💡 可以继续支付流程")
                                else:
                                    print("   ⚠️ 未找到确认支付按钮")
                            else:
                                print("   ⏭️ 跳过缴费步骤")
                        else:
                            print("   ❌ 未找到缴费按钮")
                    except Exception as e:
                        print(f"   ❌ 查找缴费按钮异常: {e}")

                    return True
                else:
                    print("   ❌ 查询无结果或页面无变化")
                    return False

            except Exception as e:
                print(f"   ❌ 点击查询失败: {e}")
                return False

        except Exception as e:
            print(f"❌ 测试打单流程异常: {e}")
            return False

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("🔒 浏览器已关闭")

def main():
    """主函数"""
    print("🎯 EIR打单功能分析和自动化")
    print("=" * 50)

    automation = EIROrderAutomation()

    try:
        # 初始化浏览器
        if not automation.init_driver():
            return

        # 登录EIR系统
        if not automation.login_to_eir():
            print("❌ 登录失败，无法继续")
            return

        # 导航到打单页面
        if not automation.navigate_to_order_page():
            print("❌ 导航失败，无法继续")
            return

        # 分析页面结构
        automation.analyze_order_page()

        # 询问是否测试打单流程
        choice = input("\n是否测试打单流程？(y/n): ").strip().lower()
        if choice == 'y':
            carrier = input("请输入船公司名称: ").strip()
            so_number = input("请输入订舱号(S/O): ").strip()

            if carrier and so_number:
                automation.test_order_process(carrier, so_number)
            else:
                print("⚠️ 船公司和订舱号不能为空")

        # 询问是否保持浏览器打开
        keep_open = input("\n是否保持浏览器打开以便手动检查？(y/n): ").strip().lower()
        if keep_open == 'y':
            print("浏览器将保持打开状态，按Enter键关闭...")
            input()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        automation.close()

if __name__ == "__main__":
    main()
