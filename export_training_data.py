#!/usr/bin/env python3
"""
导出训练数据
将标注好的验证码数据导出为训练格式
"""

import sys
import os
import json
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def export_training_data():
    """导出训练数据"""
    print("📦 导出验证码训练数据")
    
    collection_dir = "data/captcha_collection"
    labels_file = "data/captcha_labeled/labels.json"
    output_file = "data/captcha_labeled/training_data.json"
    
    # 检查标注文件
    if not os.path.exists(labels_file):
        print("❌ 标注文件不存在，请先完成验证码标注")
        return False
    
    # 加载标注数据
    try:
        with open(labels_file, 'r', encoding='utf-8') as f:
            labels_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载标注文件失败: {e}")
        return False
    
    labels = labels_data.get("labels", {})
    if not labels:
        print("❌ 没有找到标注数据")
        return False
    
    print(f"📊 找到 {len(labels)} 个标注样本")
    
    # 构建训练数据
    training_data = []
    valid_count = 0
    invalid_count = 0
    
    for filename, label_info in labels.items():
        filepath = os.path.join(collection_dir, filename)
        
        # 检查文件是否存在
        if not os.path.exists(filepath):
            print(f"⚠️ 文件不存在，跳过: {filename}")
            invalid_count += 1
            continue
        
        # 检查标注是否有效
        label = label_info.get("label", "")
        if len(label) != 4 or not label.isdigit():
            print(f"⚠️ 无效标注，跳过: {filename} -> {label}")
            invalid_count += 1
            continue
        
        # 分析图片特征
        features = analyze_image_features(filepath)
        
        # 构建训练样本
        sample = {
            "filename": filename,
            "filepath": filepath,
            "label": label,
            "confidence": label_info.get("confidence", 1.0),
            "labeled_time": label_info.get("labeled_time"),
            "features": features
        }
        
        training_data.append(sample)
        valid_count += 1
    
    print(f"✅ 有效样本: {valid_count} 个")
    print(f"⚠️ 无效样本: {invalid_count} 个")
    
    if valid_count == 0:
        print("❌ 没有有效的训练样本")
        return False
    
    # 保存训练数据
    try:
        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 训练数据已导出: {output_file}")
        print(f"📊 包含 {len(training_data)} 个训练样本")
        
        # 显示数字分布统计
        digit_count = {}
        for sample in training_data:
            label = sample["label"]
            for digit in label:
                digit_count[digit] = digit_count.get(digit, 0) + 1
        
        print(f"\n🔢 数字分布统计:")
        for digit in "0123456789":
            count = digit_count.get(digit, 0)
            print(f"  {digit}: {count} 次")
        
        return True
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return False

def analyze_image_features(filepath):
    """分析图片特征"""
    try:
        import cv2
        import numpy as np
        
        img = cv2.imread(filepath)
        if img is None:
            return {}
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        # 基本特征
        features = {
            "width": width,
            "height": height,
            "aspect_ratio": width / height,
            "file_size": os.path.getsize(filepath)
        }
        
        # 像素统计
        features["mean_intensity"] = float(np.mean(gray))
        features["std_intensity"] = float(np.std(gray))
        features["min_intensity"] = int(np.min(gray))
        features["max_intensity"] = int(np.max(gray))
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150)
        features["edge_density"] = float(np.sum(edges) / (width * height))
        
        # 二值化分析
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        features["black_pixel_ratio"] = float(np.sum(binary == 0) / (width * height))
        
        return features
        
    except Exception as e:
        print(f"⚠️ 特征分析失败 {filepath}: {e}")
        return {}

def check_training_readiness():
    """检查训练准备情况"""
    print("🔍 检查训练准备情况")
    
    labels_file = "data/captcha_labeled/labels.json"
    training_file = "data/captcha_labeled/training_data.json"
    
    # 检查标注数据
    if not os.path.exists(labels_file):
        print("❌ 标注文件不存在")
        return False
    
    try:
        with open(labels_file, 'r', encoding='utf-8') as f:
            labels_data = json.load(f)
        
        labeled_count = len(labels_data.get("labels", {}))
        print(f"📊 已标注样本: {labeled_count} 个")
        
        if labeled_count < 50:
            print(f"⚠️ 建议至少标注50个样本才开始训练 (当前: {labeled_count})")
            return False
        
    except Exception as e:
        print(f"❌ 读取标注文件失败: {e}")
        return False
    
    # 检查训练数据文件
    if os.path.exists(training_file):
        try:
            with open(training_file, 'r', encoding='utf-8') as f:
                training_data = json.load(f)
            
            training_count = len(training_data)
            print(f"📦 训练数据文件存在: {training_count} 个样本")
            
            if training_count != labeled_count:
                print("⚠️ 训练数据与标注数据不同步，建议重新导出")
                return False
            
        except Exception as e:
            print(f"❌ 读取训练数据文件失败: {e}")
            return False
    else:
        print("📦 训练数据文件不存在，需要导出")
        return False
    
    print("✅ 训练准备就绪")
    return True

def main():
    """主函数"""
    print("🎯 验证码训练数据导出工具")
    print("=" * 50)
    
    choice = input("""
选择操作:
1. 检查训练准备情况
2. 导出训练数据
3. 检查并导出 (推荐)
请输入选择 (1-3): """).strip()
    
    if choice == "1":
        check_training_readiness()
    
    elif choice == "2":
        export_training_data()
    
    elif choice == "3":
        print("🔍 检查训练准备情况...")
        if not check_training_readiness():
            print("\n📦 导出训练数据...")
            if export_training_data():
                print("\n🔍 重新检查...")
                check_training_readiness()
        else:
            print("✅ 训练数据已准备就绪")
    
    else:
        print("无效选择，执行检查并导出")
        if not check_training_readiness():
            export_training_data()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
