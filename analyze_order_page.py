#!/usr/bin/env python3
"""
分析EIR打单页面的实际元素结构
在成功导航到打单页面后分析页面元素
"""

import sys
import os
import asyncio
import time

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def analyze_order_page():
    """分析打单页面"""
    print("🔍 EIR打单页面元素分析")
    print("=" * 50)
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import Select
        
        # 创建必要的目录
        Config.create_directories()
        
        print("🔧 初始化EIR服务...")
        eir_service = EIRService()
        
        print("🔐 执行自动登录...")
        login_result = await eir_service.auto_login()
        
        if not login_result.get("success"):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        print("✅ 登录成功！")
        
        # 获取浏览器实例
        automation = eir_service.automation
        driver = automation.driver
        
        if not driver:
            print("❌ 无法获取浏览器实例")
            return False
        
        # 等待页面完全加载
        time.sleep(3)
        
        # 导航到打单页面
        print("\n🧭 导航到打单页面...")
        
        try:
            # 点击业务办理菜单
            business_menu = driver.find_element(By.ID, "UCHead1_rpFirstMeun_ctl00_liClass")
            business_link = business_menu.find_element(By.TAG_NAME, "a")
            business_link.click()
            time.sleep(2)
            print("✅ 业务办理菜单点击成功")
            
            # 点击出口EIR缴费并打单
            export_eir_menu = driver.find_element(By.ID, "UCHead1_rpSecondMenu_ctl00_liSubClass")
            export_eir_link = export_eir_menu.find_element(By.TAG_NAME, "a")
            export_eir_link.click()
            time.sleep(5)  # 等待更长时间确保页面完全加载
            print("✅ 出口EIR缴费并打单菜单点击成功")
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
        
        # 分析当前页面
        print("\n📄 分析当前页面...")
        
        current_url = driver.current_url
        page_title = driver.title
        print(f"当前URL: {current_url}")
        print(f"页面标题: {page_title}")
        
        # 等待页面完全加载
        print("\n⏳ 等待页面完全加载...")
        time.sleep(5)
        
        # 分析所有select元素
        print("\n🔍 分析所有select元素...")
        
        selects = driver.find_elements(By.TAG_NAME, "select")
        print(f"找到 {len(selects)} 个select元素:")
        
        for i, select_elem in enumerate(selects):
            try:
                select_name = select_elem.get_attribute("name")
                select_id = select_elem.get_attribute("id")
                select_class = select_elem.get_attribute("class")
                
                print(f"\n  Select {i+1}:")
                print(f"    name: {select_name}")
                print(f"    id: {select_id}")
                print(f"    class: {select_class}")
                
                # 获取选项
                try:
                    select_obj = Select(select_elem)
                    options = [option.text for option in select_obj.options]
                    print(f"    选项数量: {len(options)}")
                    
                    if options:
                        print(f"    前5个选项: {options[:5]}")
                        
                        # 检查是否是船公司选择框
                        if any('航运' in opt or 'LINE' in opt or 'SHIPPING' in opt for opt in options[:10]):
                            print(f"    🚢 可能是船公司选择框")
                        
                        # 检查是否是地点选择框
                        if any('码头' in opt or '堆场' in opt or 'Terminal' in opt for opt in options[:10]):
                            print(f"    📍 可能是地点选择框")
                
                except Exception as e:
                    print(f"    ⚠️ 无法获取选项: {e}")
                
            except Exception as e:
                print(f"  Select {i+1}: 无法获取属性 - {e}")
        
        # 分析所有input元素
        print(f"\n🔍 分析所有input元素...")
        
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"找到 {len(inputs)} 个input元素:")
        
        for i, input_elem in enumerate(inputs):
            try:
                input_type = input_elem.get_attribute("type")
                input_name = input_elem.get_attribute("name")
                input_id = input_elem.get_attribute("id")
                input_value = input_elem.get_attribute("value")
                input_placeholder = input_elem.get_attribute("placeholder")
                input_class = input_elem.get_attribute("class")
                
                # 只显示相关的input元素
                if (input_type in ['text', 'button', 'submit', 'image'] or 
                    any(keyword in str(input_name).lower() for keyword in ['booking', 'so', 'query', 'search']) or
                    any(keyword in str(input_id).lower() for keyword in ['booking', 'so', 'query', 'search']) or
                    any(keyword in str(input_value).lower() for keyword in ['查询', 'query', 'search']) if input_value else False):
                    
                    print(f"\n  Input {i+1}:")
                    print(f"    type: {input_type}")
                    print(f"    name: {input_name}")
                    print(f"    id: {input_id}")
                    print(f"    value: {input_value}")
                    print(f"    placeholder: {input_placeholder}")
                    print(f"    class: {input_class}")
                    
                    # 检查是否是订舱号输入框
                    if (input_type == 'text' and 
                        (any(keyword in str(input_name).lower() for keyword in ['booking', 'so']) if input_name else False or
                         any(keyword in str(input_id).lower() for keyword in ['booking', 'so']) if input_id else False)):
                        print(f"    📋 可能是订舱号输入框")
                    
                    # 检查是否是查询按钮
                    if (input_type in ['button', 'submit', 'image'] and
                        (any(keyword in str(input_value).lower() for keyword in ['查询', 'query', 'search']) if input_value else False)):
                        print(f"    🔍 可能是查询按钮")
                
            except Exception as e:
                continue
        
        # 查找iframe
        print(f"\n🔍 查找iframe...")
        
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            print(f"找到 {len(iframes)} 个iframe:")
            
            for i, iframe in enumerate(iframes):
                try:
                    src = iframe.get_attribute("src")
                    name = iframe.get_attribute("name")
                    iframe_id = iframe.get_attribute("id")
                    
                    print(f"\n  iframe {i+1}:")
                    print(f"    src: {src}")
                    print(f"    name: {name}")
                    print(f"    id: {iframe_id}")
                    
                    # 尝试切换到iframe分析内容
                    if src and 'BizApply' in src:
                        print(f"    🎯 这可能是打单页面的iframe")
                        
                        try:
                            driver.switch_to.frame(iframe)
                            print(f"    ✅ 成功切换到iframe")
                            
                            # 在iframe中查找元素
                            iframe_selects = driver.find_elements(By.TAG_NAME, "select")
                            iframe_inputs = driver.find_elements(By.TAG_NAME, "input")
                            
                            print(f"    iframe内容: {len(iframe_selects)} 个select, {len(iframe_inputs)} 个input")
                            
                            # 分析iframe中的select元素
                            for j, select_elem in enumerate(iframe_selects[:5]):  # 只分析前5个
                                try:
                                    select_name = select_elem.get_attribute("name")
                                    select_id = select_elem.get_attribute("id")
                                    
                                    select_obj = Select(select_elem)
                                    options = [option.text for option in select_obj.options]
                                    
                                    print(f"      iframe Select {j+1}: name={select_name}, id={select_id}")
                                    print(f"        选项: {options[:3]}...")
                                    
                                    # 检查是否是船公司选择框
                                    if any('航运' in opt or 'LINE' in opt for opt in options[:5]):
                                        print(f"        🚢 这是船公司选择框！")
                                
                                except:
                                    continue
                            
                            # 分析iframe中的input元素
                            for j, input_elem in enumerate(iframe_inputs[:10]):  # 只分析前10个
                                try:
                                    input_type = input_elem.get_attribute("type")
                                    input_name = input_elem.get_attribute("name")
                                    input_id = input_elem.get_attribute("id")
                                    input_value = input_elem.get_attribute("value")
                                    
                                    if input_type in ['text', 'button', 'submit', 'image']:
                                        print(f"      iframe Input {j+1}: type={input_type}, name={input_name}, id={input_id}, value={input_value}")
                                        
                                        # 检查是否是订舱号输入框
                                        if input_type == 'text' and any(keyword in str(input_name).lower() for keyword in ['booking', 'so']) if input_name else False:
                                            print(f"        📋 这是订舱号输入框！")
                                        
                                        # 检查是否是查询按钮
                                        if input_type in ['button', 'submit', 'image'] and any(keyword in str(input_value).lower() for keyword in ['查询', 'query']) if input_value else False:
                                            print(f"        🔍 这是查询按钮！")
                                
                                except:
                                    continue
                            
                            # 切换回主页面
                            driver.switch_to.default_content()
                            
                        except Exception as e:
                            print(f"    ❌ 无法访问iframe: {e}")
                            driver.switch_to.default_content()
                
                except:
                    continue
        else:
            print("未找到iframe")
        
        # 保存页面源码
        print(f"\n💾 保存页面源码...")
        
        try:
            page_source = driver.page_source
            timestamp = int(time.time())
            source_file = f"order_page_source_{timestamp}.html"
            
            with open(source_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            print(f"页面源码已保存: {source_file}")
            
        except Exception as e:
            print(f"保存页面源码失败: {e}")
        
        # 截图
        try:
            screenshot_file = f"order_page_screenshot_{int(time.time())}.png"
            driver.save_screenshot(screenshot_file)
            print(f"页面截图已保存: {screenshot_file}")
            
        except Exception as e:
            print(f"保存截图失败: {e}")
        
        # 询问是否保持浏览器打开
        choice = input("\n是否保持浏览器打开以便手动检查？(y/n): ").strip().lower()
        
        if choice == 'y':
            print("浏览器将保持打开状态...")
            print("您可以手动查看页面结构，寻找正确的元素")
            input("完成后按 Enter 键关闭浏览器...")
        
        eir_service.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 页面分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 EIR打单页面元素分析工具")
    print("=" * 50)
    
    choice = input("\n是否开始分析打单页面？(y/n): ").strip().lower()
    
    if choice == 'y':
        print("\n🚀 开始分析打单页面...")
        success = asyncio.run(analyze_order_page())
        
        if success:
            print("\n✅ 页面分析完成")
            print("📋 分析结果已保存为HTML文件和截图")
        else:
            print("\n❌ 页面分析失败")
    else:
        print("👋 退出分析")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断分析")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
