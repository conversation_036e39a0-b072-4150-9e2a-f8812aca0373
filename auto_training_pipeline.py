#!/usr/bin/env python3
"""
自动化验证码训练流水线
完整的从收集到训练的自动化流程
"""

import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class AutoTrainingPipeline:
    def __init__(self):
        self.collection_dir = "data/captcha_collection"
        self.labels_dir = "data/captcha_labeled"
        self.models_dir = "data/models"
        self.reports_dir = "data/reports"
        
        # 创建必要目录
        for dir_path in [self.collection_dir, self.labels_dir, self.models_dir, self.reports_dir]:
            os.makedirs(dir_path, exist_ok=True)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统当前状态"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "collection": self.get_collection_status(),
            "labeling": self.get_labeling_status(),
            "training": self.get_training_status()
        }
        return status
    
    def get_collection_status(self) -> Dict[str, Any]:
        """获取收集状态"""
        try:
            metadata_file = os.path.join(self.collection_dir, "metadata.json")
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                return {
                    "total_collected": metadata.get("total_collected", 0),
                    "success_count": metadata.get("statistics", {}).get("success_count", 0),
                    "failed_count": metadata.get("statistics", {}).get("failed_count", 0),
                    "sessions": len(metadata.get("collection_sessions", [])),
                    "status": "completed" if metadata.get("total_collected", 0) > 0 else "empty"
                }
            else:
                # 直接扫描文件夹
                if os.path.exists(self.collection_dir):
                    files = [f for f in os.listdir(self.collection_dir) if f.endswith('.png')]
                    return {
                        "total_collected": len(files),
                        "success_count": len(files),
                        "failed_count": 0,
                        "sessions": 1 if files else 0,
                        "status": "completed" if files else "empty"
                    }
                
                return {
                    "total_collected": 0,
                    "success_count": 0,
                    "failed_count": 0,
                    "sessions": 0,
                    "status": "empty"
                }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def get_labeling_status(self) -> Dict[str, Any]:
        """获取标注状态"""
        try:
            labels_file = os.path.join(self.labels_dir, "labels.json")
            if os.path.exists(labels_file):
                with open(labels_file, 'r', encoding='utf-8') as f:
                    labels = json.load(f)
                
                labeled_count = len(labels.get("labels", {}))
                collection_status = self.get_collection_status()
                total_collected = collection_status.get("total_collected", 0)
                unlabeled_count = total_collected - labeled_count
                
                return {
                    "labeled_count": labeled_count,
                    "unlabeled_count": unlabeled_count,
                    "total_count": total_collected,
                    "progress": (labeled_count / total_collected * 100) if total_collected > 0 else 0,
                    "digit_frequency": labels.get("statistics", {}).get("digit_frequency", {}),
                    "status": "completed" if unlabeled_count == 0 and labeled_count > 0 else "in_progress" if labeled_count > 0 else "not_started"
                }
            else:
                collection_status = self.get_collection_status()
                total_collected = collection_status.get("total_collected", 0)
                
                return {
                    "labeled_count": 0,
                    "unlabeled_count": total_collected,
                    "total_count": total_collected,
                    "progress": 0,
                    "digit_frequency": {},
                    "status": "not_started"
                }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态"""
        try:
            training_info_file = os.path.join(self.models_dir, "training_info.json")
            if os.path.exists(training_info_file):
                with open(training_info_file, 'r', encoding='utf-8') as f:
                    training_info = json.load(f)
                
                # 检查模型文件是否存在
                model_files = [
                    "digit_model_position_0.joblib",
                    "digit_model_position_1.joblib", 
                    "digit_model_position_2.joblib",
                    "digit_model_position_3.joblib",
                    "whole_captcha_model.joblib"
                ]
                
                existing_models = []
                for model_file in model_files:
                    model_path = os.path.join(self.models_dir, model_file)
                    if os.path.exists(model_path):
                        existing_models.append(model_file)
                
                return {
                    "training_time": training_info.get("training_time"),
                    "sample_count": training_info.get("sample_count", 0),
                    "models_count": len(existing_models),
                    "existing_models": existing_models,
                    "models_info": training_info.get("models", {}),
                    "status": "completed" if len(existing_models) == 5 else "partial"
                }
            else:
                return {
                    "training_time": None,
                    "sample_count": 0,
                    "models_count": 0,
                    "existing_models": [],
                    "models_info": {},
                    "status": "not_started"
                }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def print_status_report(self):
        """打印状态报告"""
        status = self.get_system_status()
        
        print("🎯 验证码训练系统状态报告")
        print("=" * 60)
        print(f"📅 检查时间: {status['timestamp']}")
        
        # 收集状态
        collection = status["collection"]
        print(f"\n📦 验证码收集状态:")
        if collection.get("status") == "error":
            print(f"❌ 收集状态检查失败: {collection.get('error')}")
        else:
            print(f"✅ 已收集: {collection.get('total_collected', 0)} 个验证码")
            print(f"✅ 成功率: {collection.get('success_count', 0)}/{collection.get('success_count', 0) + collection.get('failed_count', 0)}")
            print(f"✅ 收集会话: {collection.get('sessions', 0)} 次")
        
        # 标注状态
        labeling = status["labeling"]
        print(f"\n🏷️ 验证码标注状态:")
        if labeling.get("status") == "error":
            print(f"❌ 标注状态检查失败: {labeling.get('error')}")
        else:
            print(f"✅ 已标注: {labeling.get('labeled_count', 0)} 个")
            print(f"✅ 待标注: {labeling.get('unlabeled_count', 0)} 个")
            print(f"✅ 标注进度: {labeling.get('progress', 0):.1f}%")
            
            # 数字频率分布
            freq = labeling.get("digit_frequency", {})
            if any(count > 0 for count in freq.values()):
                print(f"✅ 数字分布: " + ", ".join([f"{d}:{c}" for d, c in freq.items() if c > 0]))
        
        # 训练状态
        training = status["training"]
        print(f"\n🤖 模型训练状态:")
        if training.get("status") == "error":
            print(f"❌ 训练状态检查失败: {training.get('error')}")
        else:
            if training.get("status") == "not_started":
                print(f"⏳ 尚未开始训练")
            else:
                print(f"✅ 训练样本: {training.get('sample_count', 0)} 个")
                print(f"✅ 已训练模型: {training.get('models_count', 0)}/5 个")
                print(f"✅ 训练时间: {training.get('training_time', 'N/A')}")
        
        # 系统建议
        self.print_recommendations(status)
    
    def print_recommendations(self, status: Dict[str, Any]):
        """打印系统建议"""
        print(f"\n💡 系统建议:")
        
        collection = status["collection"]
        labeling = status["labeling"]
        training = status["training"]
        
        # 收集建议
        total_collected = collection.get("total_collected", 0)
        if total_collected == 0:
            print("1. 🚀 开始收集验证码:")
            print("   python test_simple_collection.py")
            print("   建议先收集100-200个验证码")
        elif total_collected < 100:
            print(f"1. 📈 继续收集验证码 (当前: {total_collected}个):")
            print("   python test_simple_collection.py")
            print("   建议收集到100-200个以获得更好的训练效果")
        else:
            print(f"1. ✅ 验证码收集充足 ({total_collected}个)")
        
        # 标注建议
        labeled_count = labeling.get("labeled_count", 0)
        unlabeled_count = labeling.get("unlabeled_count", 0)
        
        if labeled_count == 0 and total_collected > 0:
            print("2. 🏷️ 开始标注验证码:")
            print("   python gui_labeling_tool.py  (推荐GUI工具)")
            print("   或 python simple_labeling.py  (命令行工具)")
        elif unlabeled_count > 0:
            progress = labeling.get("progress", 0)
            print(f"2. 📝 继续标注验证码 (进度: {progress:.1f}%):")
            print("   python gui_labeling_tool.py")
            print(f"   还需标注 {unlabeled_count} 个验证码")
        else:
            print(f"2. ✅ 验证码标注完成 ({labeled_count}个)")
        
        # 训练建议
        if training.get("status") == "not_started" and labeled_count >= 50:
            print("3. 🤖 开始训练模型:")
            print("   python train_captcha_model.py train")
            print(f"   当前有 {labeled_count} 个标注样本可用于训练")
        elif training.get("status") == "partial":
            print("3. 🔄 重新训练模型 (上次训练不完整):")
            print("   python train_captcha_model.py train")
        elif training.get("status") == "completed":
            print("3. ✅ 模型训练完成")
            print("   python train_captcha_model.py test  (测试模型)")
            print("   python train_captcha_model.py batch_test  (批量测试)")
        else:
            print(f"3. ⏳ 需要更多标注数据才能开始训练 (当前: {labeled_count}个，建议: 50+个)")
        
        # 质量建议
        if labeled_count > 0:
            freq = labeling.get("digit_frequency", {})
            min_count = min([count for count in freq.values() if count > 0], default=0)
            if min_count < 5:
                print("4. ⚠️ 数据质量建议:")
                print("   某些数字出现次数较少，建议继续标注以平衡数据分布")
    
    def auto_collect(self, count: int = 100) -> bool:
        """自动收集验证码"""
        print(f"🚀 自动收集 {count} 个验证码...")
        
        try:
            from backend.app.services.simple_captcha_collector import simple_captcha_collector
            
            result = simple_captcha_collector.collect_captchas(count, min(20, count//5))
            
            if result.get("status") == "completed":
                print(f"✅ 收集完成: 成功 {result.get('success_count', 0)} 个")
                return True
            else:
                print(f"❌ 收集失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 收集过程异常: {e}")
            return False
    
    def launch_gui_labeling(self):
        """启动GUI标注工具"""
        print("🏷️ 启动GUI标注工具...")
        
        try:
            import subprocess
            import sys
            
            # 启动GUI标注工具
            subprocess.Popen([sys.executable, "gui_labeling_tool.py"])
            print("✅ GUI标注工具已启动")
            print("💡 请在GUI界面中完成验证码标注")
            
        except Exception as e:
            print(f"❌ 启动GUI工具失败: {e}")
            print("💡 请手动运行: python gui_labeling_tool.py")
    
    def auto_train(self) -> bool:
        """自动训练模型"""
        print("🤖 开始自动训练模型...")
        
        try:
            from backend.app.services.captcha_trainer import captcha_trainer
            
            result = captcha_trainer.train_models()
            
            if result.get("status") == "success":
                print("✅ 模型训练完成!")
                print(f"📊 训练样本: {result.get('sample_count', 0)} 个")
                print(f"📈 分位置模型平均准确率: {result.get('average_digit_accuracy', 0):.3f}")
                print(f"📈 整体模型准确率: {result.get('whole_model_accuracy', 0):.3f}")
                return True
            else:
                print(f"❌ 训练失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 训练过程异常: {e}")
            return False
    
    def auto_test(self) -> bool:
        """自动测试模型"""
        print("🧪 开始自动测试模型...")
        
        try:
            from backend.app.services.captcha_trainer import captcha_trainer
            
            # 加载模型
            models = captcha_trainer.load_trained_models()
            if not models:
                print("❌ 没有找到训练好的模型")
                return False
            
            print("✅ 模型加载成功")
            
            # 测试几个验证码
            collection_dir = "data/captcha_collection"
            if os.path.exists(collection_dir):
                test_files = [f for f in os.listdir(collection_dir) if f.endswith('.png')][:5]
                
                if test_files:
                    print(f"🔍 测试 {len(test_files)} 个验证码样本:")
                    
                    for i, filename in enumerate(test_files, 1):
                        filepath = os.path.join(collection_dir, filename)
                        result = captcha_trainer.predict_captcha(filepath, models)
                        
                        if result.get("status") == "success":
                            prediction = result.get("best_prediction")
                            confidence = result.get("best_confidence", 0)
                            print(f"  {i}. {filename}: {prediction} (置信度: {confidence:.3f})")
                        else:
                            print(f"  {i}. {filename}: 预测失败")
                    
                    return True
                else:
                    print("❌ 没有找到测试图片")
                    return False
            else:
                print("❌ 验证码目录不存在")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程异常: {e}")
            return False
    
    def run_full_pipeline(self, collect_count: int = 200):
        """运行完整的训练流水线"""
        print("🎯 开始运行完整的验证码训练流水线")
        print("=" * 60)
        
        # 1. 检查当前状态
        print("第1步: 检查系统状态")
        self.print_status_report()
        
        status = self.get_system_status()
        collection = status["collection"]
        labeling = status["labeling"]
        training = status["training"]
        
        # 2. 收集验证码 (如果需要)
        total_collected = collection.get("total_collected", 0)
        if total_collected < collect_count:
            need_collect = collect_count - total_collected
            print(f"\n第2步: 收集验证码 (需要收集 {need_collect} 个)")
            
            if not self.auto_collect(need_collect):
                print("❌ 验证码收集失败，流水线终止")
                return False
        else:
            print(f"\n第2步: ✅ 验证码收集充足 ({total_collected} 个)")
        
        # 3. 启动标注工具
        labeled_count = labeling.get("labeled_count", 0)
        unlabeled_count = labeling.get("unlabeled_count", 0)
        
        if unlabeled_count > 0:
            print(f"\n第3步: 启动标注工具 (需要标注 {unlabeled_count} 个)")
            self.launch_gui_labeling()
            
            print("⏳ 等待用户完成标注...")
            print("💡 请在GUI界面中完成验证码标注，然后重新运行此脚本继续训练")
            return False
        else:
            print(f"\n第3步: ✅ 验证码标注完成 ({labeled_count} 个)")
        
        # 4. 训练模型
        if training.get("status") != "completed":
            print(f"\n第4步: 训练模型")
            
            if not self.auto_train():
                print("❌ 模型训练失败，流水线终止")
                return False
        else:
            print(f"\n第4步: ✅ 模型已训练完成")
        
        # 5. 测试模型
        print(f"\n第5步: 测试模型")
        if not self.auto_test():
            print("⚠️ 模型测试失败，但训练已完成")
        
        print(f"\n🎉 验证码训练流水线完成!")
        print("✅ 您现在可以使用训练好的模型进行验证码识别")
        
        return True

def main():
    """主函数"""
    pipeline = AutoTrainingPipeline()
    
    print("🎯 验证码训练自动化流水线")
    print("=" * 50)
    
    choice = input("""
选择操作:
1. 查看系统状态
2. 自动收集验证码 (100个)
3. 启动GUI标注工具
4. 自动训练模型
5. 自动测试模型
6. 运行完整流水线
请输入选择 (1-6): """).strip()
    
    if choice == "1":
        pipeline.print_status_report()
    
    elif choice == "2":
        count = input("输入收集数量 (默认100): ").strip()
        count = int(count) if count.isdigit() else 100
        pipeline.auto_collect(count)
    
    elif choice == "3":
        pipeline.launch_gui_labeling()
    
    elif choice == "4":
        pipeline.auto_train()
    
    elif choice == "5":
        pipeline.auto_test()
    
    elif choice == "6":
        count = input("输入目标收集数量 (默认200): ").strip()
        count = int(count) if count.isdigit() else 200
        pipeline.run_full_pipeline(count)
    
    else:
        print("无效选择，显示系统状态")
        pipeline.print_status_report()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
