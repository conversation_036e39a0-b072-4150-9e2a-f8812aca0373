#!/usr/bin/env python3
"""
命令行版本验证码标注工具
适用于GUI无法显示的情况
"""

import sys
import os
import json
import subprocess
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class ConsoleCaptchaLabeler:
    def __init__(self):
        self.collection_dir = "data/captcha_collection"
        self.labels_file = "data/captcha_labeled/labels.json"
        self.labels_dir = "data/captcha_labeled"
        
        # 创建标注目录
        os.makedirs(self.labels_dir, exist_ok=True)
        
        # 加载数据
        self.labels = self.load_labels()
        self.unlabeled_files = self.get_unlabeled_files()
    
    def load_labels(self):
        """加载标注数据"""
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "labeled_count": 0,
            "labels": {},
            "statistics": {
                "digit_frequency": {str(i): 0 for i in range(10)}
            }
        }
    
    def save_labels(self):
        """保存标注数据"""
        try:
            with open(self.labels_file, 'w', encoding='utf-8') as f:
                json.dump(self.labels, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def get_unlabeled_files(self):
        """获取未标注的文件列表"""
        if not os.path.exists(self.collection_dir):
            return []
        
        all_files = [f for f in os.listdir(self.collection_dir) if f.endswith('.png')]
        labeled_files = set(self.labels.get("labels", {}).keys())
        
        unlabeled = [f for f in all_files if f not in labeled_files]
        return sorted(unlabeled)
    
    def show_progress(self):
        """显示当前进度"""
        labeled_count = len(self.labels.get("labels", {}))
        unlabeled_count = len(self.unlabeled_files)
        total_count = labeled_count + unlabeled_count
        
        print(f"\n📊 标注进度:")
        print(f"总验证码数: {total_count}")
        print(f"已标注: {labeled_count}")
        print(f"待标注: {unlabeled_count}")
        
        if total_count > 0:
            progress = labeled_count / total_count * 100
            print(f"完成度: {progress:.1f}%")
            
            # 进度条
            bar_length = 30
            filled = int(bar_length * progress / 100)
            bar = "█" * filled + "░" * (bar_length - filled)
            print(f"进度条: [{bar}] {progress:.1f}%")
    
    def show_ascii_preview(self, filepath):
        """显示ASCII预览"""
        try:
            import cv2
            import numpy as np
            
            img = cv2.imread(filepath, cv2.IMREAD_GRAYSCALE)
            if img is None:
                print("⚠️ 无法读取图片")
                return
            
            # 调整尺寸
            height, width = img.shape
            new_width = 40
            new_height = int(height * new_width / width * 0.5)
            resized = cv2.resize(img, (new_width, new_height))
            
            # 二值化
            _, binary = cv2.threshold(resized, 127, 255, cv2.THRESH_BINARY)
            
            print(f"\n🎨 验证码预览:")
            print("┌" + "─" * new_width + "┐")
            
            for row in binary:
                line = "│"
                for pixel in row:
                    if pixel < 128:
                        line += "█"
                    else:
                        line += " "
                line += "│"
                print(line)
            
            print("└" + "─" * new_width + "┘")
            
        except Exception as e:
            print(f"⚠️ 预览生成失败: {e}")
    
    def open_image(self, filepath):
        """尝试打开图片"""
        try:
            # Windows
            subprocess.run(['start', filepath], shell=True, check=True)
            print(f"📁 已打开图片: {filepath}")
        except:
            try:
                # 尝试其他方法
                os.startfile(filepath)
                print(f"📁 已打开图片: {filepath}")
            except:
                print(f"📁 请手动打开图片: {filepath}")
    
    def add_label(self, filename, label):
        """添加标注"""
        self.labels["labels"][filename] = {
            "label": label,
            "labeled_time": datetime.now().isoformat()
        }
        
        # 更新统计
        self.labels["labeled_count"] = len(self.labels["labels"])
        
        for digit in label:
            if digit in self.labels["statistics"]["digit_frequency"]:
                self.labels["statistics"]["digit_frequency"][digit] += 1
        
        return self.save_labels()
    
    def start_labeling(self, max_count=50):
        """开始标注"""
        if not self.unlabeled_files:
            print("✅ 没有需要标注的验证码")
            return
        
        print(f"🏷️ 开始验证码标注")
        print("=" * 50)
        
        self.show_progress()
        
        print(f"\n🎯 本次标注目标: {min(max_count, len(self.unlabeled_files))} 个")
        print("\n操作说明:")
        print("- 输入4位数字进行标注")
        print("- 输入 'o' 打开图片文件")
        print("- 输入 's' 跳过当前验证码")
        print("- 输入 'q' 退出标注")
        print("- 输入 'stats' 查看统计")
        
        labeled_this_session = 0
        skipped_this_session = 0
        
        try:
            for i, filename in enumerate(self.unlabeled_files[:max_count]):
                filepath = os.path.join(self.collection_dir, filename)
                
                print(f"\n{'='*60}")
                print(f"进度: {i+1}/{min(max_count, len(self.unlabeled_files))}")
                print(f"文件: {filename}")
                print(f"路径: {filepath}")
                
                # 显示ASCII预览
                self.show_ascii_preview(filepath)
                
                while True:
                    user_input = input(f"\n请输入验证码 (4位数字): ").strip()
                    
                    if user_input.lower() == 'q':
                        print("👋 退出标注")
                        return
                    
                    elif user_input.lower() == 's':
                        print("⏭️ 跳过此验证码")
                        skipped_this_session += 1
                        break
                    
                    elif user_input.lower() == 'o':
                        self.open_image(filepath)
                        continue
                    
                    elif user_input.lower() == 'stats':
                        self.show_progress()
                        continue
                    
                    elif len(user_input) == 4 and user_input.isdigit():
                        if self.add_label(filename, user_input):
                            print(f"✅ 标注成功: {filename} -> {user_input}")
                            labeled_this_session += 1
                            break
                        else:
                            print("❌ 标注保存失败")
                    
                    else:
                        print("❌ 请输入4位数字，或使用命令: o(打开) s(跳过) q(退出) stats(统计)")
                
                # 每5个保存一次进度显示
                if (i + 1) % 5 == 0:
                    print(f"\n💾 已完成 {i + 1} 个，自动保存")
                    self.show_progress()
        
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断标注")
        
        finally:
            print(f"\n📊 本次标注结果:")
            print(f"已标注: {labeled_this_session} 个")
            print(f"跳过: {skipped_this_session} 个")
            print(f"总标注数: {len(self.labels.get('labels', {}))}")
            
            # 显示最终进度
            self.show_progress()
    
    def show_stats(self):
        """显示详细统计"""
        labeled_count = len(self.labels.get("labels", {}))
        unlabeled_count = len(self.unlabeled_files)
        
        print(f"\n📊 详细统计信息:")
        print(f"已标注: {labeled_count} 个")
        print(f"未标注: {unlabeled_count} 个")
        
        # 数字频率
        freq = self.labels.get("statistics", {}).get("digit_frequency", {})
        print(f"\n🔢 数字频率分布:")
        for digit in "0123456789":
            count = freq.get(digit, 0)
            print(f"  {digit}: {count:3d} 次")

def main():
    """主函数"""
    print("🏷️ 命令行验证码标注工具")
    print("=" * 40)
    
    labeler = ConsoleCaptchaLabeler()
    
    # 显示当前状态
    labeler.show_stats()
    
    choice = input(f"\n选择操作:\n1. 开始标注 (20个)\n2. 开始标注 (50个)\n3. 查看统计\n4. 自定义数量\n请选择 (1-4): ").strip()
    
    if choice == "1":
        labeler.start_labeling(20)
    elif choice == "2":
        labeler.start_labeling(50)
    elif choice == "3":
        labeler.show_stats()
    elif choice == "4":
        try:
            count = int(input("请输入标注数量: "))
            labeler.start_labeling(count)
        except:
            print("无效数量，默认标注20个")
            labeler.start_labeling(20)
    else:
        print("默认开始标注20个")
        labeler.start_labeling(20)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
