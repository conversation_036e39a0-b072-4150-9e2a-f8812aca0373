#!/usr/bin/env python3
"""
测试ChromeDriver是否正常工作
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_chromedriver():
    """测试ChromeDriver"""
    print("=== ChromeDriver测试 ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("1. 正在初始化ChromeDriver...")
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式，不显示浏览器窗口
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        # 使用webdriver-manager自动下载和管理ChromeDriver
        print("2. 下载/检查ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        
        print("3. 启动Chrome浏览器...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("4. 测试基本功能...")
        # 访问一个简单的页面
        driver.get("https://www.baidu.com")
        
        # 获取页面标题
        title = driver.title
        print(f"✅ 页面标题: {title}")
        
        # 关闭浏览器
        driver.quit()
        
        print("✅ ChromeDriver测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver测试失败: {e}")
        return False

def test_eir_automation():
    """测试EIR自动化模块"""
    print("\n=== EIR自动化模块测试 ===")
    
    try:
        from backend.app.automation.eir_automation import EIRAutomation
        
        print("1. 创建EIR自动化实例...")
        eir = EIRAutomation()
        
        print("2. 初始化浏览器驱动...")
        if eir.init_driver():
            print("✅ 浏览器驱动初始化成功")
            
            print("3. 测试访问EIR网站...")
            try:
                eir.driver.get("https://www.baidu.com")  # 先测试访问百度
                title = eir.driver.title
                print(f"✅ 测试页面访问成功: {title}")
            except Exception as e:
                print(f"⚠️  页面访问测试失败: {e}")
            
            print("4. 关闭浏览器...")
            eir.close()
            print("✅ EIR自动化模块测试成功")
            return True
        else:
            print("❌ 浏览器驱动初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ EIR自动化模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始ChromeDriver和EIR自动化测试...")
    
    # 测试基础ChromeDriver
    chromedriver_ok = test_chromedriver()
    
    # 测试EIR自动化模块
    eir_ok = test_eir_automation()
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"ChromeDriver基础测试: {'✅ 通过' if chromedriver_ok else '❌ 失败'}")
    print(f"EIR自动化模块测试: {'✅ 通过' if eir_ok else '❌ 失败'}")
    
    if chromedriver_ok and eir_ok:
        print("\n🎉 所有测试通过！")
        print("现在可以进行完整的EIR登录测试了")
        print("运行命令: python test_login.py")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
