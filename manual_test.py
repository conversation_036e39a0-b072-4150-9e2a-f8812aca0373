#!/usr/bin/env python3
"""
手动测试脚本 - 登录后保持浏览器打开进行手动分析
"""

import sys
import os
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def manual_test():
    """手动测试"""
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        
        Config.create_directories()
        eir_service = EIRService()
        
        print("🔐 执行自动登录...")
        result = await eir_service.auto_login()
        
        if result.get("success"):
            print("✅ 登录成功！")
            print("🌐 浏览器已打开EIR系统")
            print("📋 请手动查找【业务办理】->【出口EIR缴费并打单】")
            
            # 获取当前页面信息
            automation = eir_service.automation
            if automation and automation.driver:
                current_url = automation.driver.current_url
                page_title = automation.driver.title
                
                print(f"📄 当前页面: {page_title}")
                print(f"🌐 URL: {current_url}")
            
            print("\n💡 查找提示:")
            print("1. 查看页面顶部或左侧的导航菜单")
            print("2. 寻找'业务办理'或'Business'相关的菜单")
            print("3. 点击后查找'出口EIR'或'Export EIR'选项")
            print("4. 注意可能的iframe或弹出窗口")
            
            input("\n按 Enter 键关闭浏览器...")
            eir_service.close()
        else:
            print(f"❌ 登录失败: {result.get('message')}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(manual_test())
