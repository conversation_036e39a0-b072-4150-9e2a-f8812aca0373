#!/usr/bin/env python3
"""
简化版启动脚本 - 跳过ChromeDriver检查
"""

import sys
import os
import webbrowser
import time
import threading

def start_server():
    """启动服务器"""
    print("启动FastAPI服务器...")

    # 添加backend目录到Python路径
    backend_path = os.path.join(os.path.dirname(__file__), 'backend')
    sys.path.insert(0, backend_path)

    try:
        import uvicorn
        from backend.app.main import app
        from backend.config import Config

        # 创建必要的目录
        Config.create_directories()

        print("✅ 服务器启动中...")
        print("📡 API地址: http://localhost:8001")
        print("🌐 API文档: http://localhost:8001/docs")
        print("🧪 测试页面: frontend/index.html")
        print("⚠️  注意: 需要安装ChromeDriver才能使用自动化功能")
        print("🛑 按 Ctrl+C 停止服务器")
        print("-" * 50)

        # 延迟打开浏览器
        def open_browser():
            time.sleep(3)
            frontend_path = os.path.join(os.path.dirname(__file__), 'frontend', 'index.html')
            if os.path.exists(frontend_path):
                print(f"🚀 正在打开测试页面...")
                webbrowser.open(f'file://{os.path.abspath(frontend_path)}')

        threading.Thread(target=open_browser, daemon=True).start()

        # 启动服务器
        uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")

    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        print("请检查是否有其他程序占用8000端口")

if __name__ == "__main__":
    print("=== EIR登录测试系统 (简化版) ===")
    start_server()
