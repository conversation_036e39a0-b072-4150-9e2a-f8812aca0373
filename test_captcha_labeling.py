#!/usr/bin/env python3
"""
验证码标注测试
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_labeling(batch_size=10):
    """测试验证码标注"""
    print(f"🏷️ 开始验证码标注测试 (批次大小: {batch_size})")
    
    try:
        from backend.app.services.captcha_labeler import captcha_labeler
        
        # 显示当前统计
        stats = captcha_labeler.get_labeling_statistics()
        print(f"\n📊 当前状态:")
        print(f"总收集数量: {stats['total_collected']}")
        print(f"已标注数量: {stats['labeled_count']}")
        print(f"未标注数量: {stats['unlabeled_count']}")
        print(f"标注进度: {stats['labeling_progress']:.1f}%")
        
        if stats['unlabeled_count'] == 0:
            print("✅ 所有验证码都已标注完成！")
            return True
        
        print(f"\n🎯 准备标注 {min(batch_size, stats['unlabeled_count'])} 个验证码")
        print("标注说明:")
        print("- 输入4位数字进行标注 (如: 1234)")
        print("- 输入 's' 跳过当前验证码")
        print("- 输入 'q' 退出标注")
        print("- 输入 'stats' 查看统计信息")
        
        # 开始交互式标注
        result = captcha_labeler.batch_label_interactive(batch_size)
        
        print(f"\n📊 标注结果:")
        print(f"状态: {result.get('status')}")
        print(f"已标注: {result.get('labeled_count', 0)} 个")
        print(f"跳过: {result.get('skipped_count', 0)} 个")
        
        if result.get('status') == 'completed':
            print("✅ 标注完成！")
            return True
        elif result.get('status') == 'interrupted':
            print("⚠️ 标注被中断")
            return False
        else:
            print(f"❌ 标注失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 标注过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_labeling_stats():
    """显示标注统计"""
    print("📊 标注统计信息")
    
    try:
        from backend.app.services.captcha_labeler import captcha_labeler
        
        stats = captcha_labeler.get_labeling_statistics()
        print(f"\n📈 总体统计:")
        print(f"总收集数量: {stats['total_collected']}")
        print(f"已标注数量: {stats['labeled_count']}")
        print(f"未标注数量: {stats['unlabeled_count']}")
        print(f"标注进度: {stats['labeling_progress']:.1f}%")
        print(f"标注会话: {stats['sessions_count']} 次")
        
        if stats['labeled_count'] > 0:
            print(f"\n🔢 数字频率分布:")
            for digit, count in stats['digit_frequency'].items():
                if count > 0:
                    print(f"  {digit}: {count} 次")
            
            print(f"\n📏 长度分布:")
            for length, count in stats['length_distribution'].items():
                print(f"  {length}位: {count} 个")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def export_training_data():
    """导出训练数据"""
    print("📦 导出训练数据")
    
    try:
        from backend.app.services.captcha_labeler import captcha_labeler
        
        result = captcha_labeler.export_training_data()
        
        if result.get('status') == 'success':
            print(f"✅ 训练数据导出成功！")
            print(f"文件路径: {result.get('training_data_file')}")
            print(f"样本数量: {result.get('sample_count')}")
            print(f"导出时间: {result.get('export_time')}")
            return True
        else:
            print(f"❌ 导出失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 导出过程发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🎯 验证码标注测试")
    print("=" * 50)
    
    choice = input("""
选择操作:
1. 开始标注 (10个)
2. 开始标注 (20个)
3. 查看统计信息
4. 导出训练数据
请输入选择 (1/2/3/4): """).strip()
    
    if choice == "1":
        success = test_labeling(10)
    elif choice == "2":
        success = test_labeling(20)
    elif choice == "3":
        show_labeling_stats()
        success = True
    elif choice == "4":
        success = export_training_data()
    else:
        print("无效选择，默认开始标注10个")
        success = test_labeling(10)
    
    if success:
        print("\n🎉 操作成功完成！")
        
        # 显示最新统计
        print("\n📊 最新统计:")
        show_labeling_stats()
    else:
        print("\n❌ 操作失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
