#!/usr/bin/env python3
"""
验证码自动识别功能测试
"""

import sys
import os
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_captcha_recognition():
    """测试验证码自动识别功能"""
    print("=== 验证码自动识别功能测试 ===")
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.app.services.captcha_service import captcha_service
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务...")
        eir_service = EIRService()
        
        print("2. 获取验证码...")
        result = await eir_service.get_login_captcha()
        print(f"验证码获取结果: {result}")
        
        if result["success"]:
            captcha_path = result["captcha_path"]
            print(f"✅ 验证码保存在: {captcha_path}")
            
            # 测试验证码自动识别
            print("\n3. 测试验证码自动识别...")
            recognition_result = captcha_service.recognize_captcha(captcha_path)
            print(f"识别结果: {recognition_result}")
            
            if recognition_result["success"]:
                best_result = recognition_result["best_result"]
                confidence = recognition_result["confidence"]
                all_results = recognition_result["all_results"]
                
                print(f"✅ 最佳识别结果: {best_result}")
                print(f"✅ 置信度: {confidence}")
                print(f"✅ 所有识别结果: {all_results}")
                
                # 询问用户是否使用自动识别的结果进行登录
                user_choice = input(f"\n是否使用识别结果 '{best_result}' 进行自动登录？(y/n): ").strip().lower()
                
                if user_choice == 'y':
                    print(f"\n4. 使用自动识别结果 '{best_result}' 进行登录...")
                    login_result = await eir_service.login(best_result)
                    print(f"登录结果: {login_result}")
                    
                    if login_result["success"]:
                        print("🎉 自动登录成功！")
                    else:
                        print(f"❌ 自动登录失败: {login_result['message']}")
                        
                        # 如果自动识别失败，尝试手动输入
                        manual_code = input("请手动输入验证码: ").strip()
                        if manual_code:
                            print(f"使用手动输入的验证码 '{manual_code}' 重试...")
                            retry_result = await eir_service.login(manual_code)
                            print(f"手动登录结果: {retry_result}")
                else:
                    print("跳过自动登录测试")
                    
                # 测试完整的自动登录流程
                print("\n5. 测试完整的自动登录流程...")
                auto_login_result = await eir_service.auto_login()
                print(f"自动登录流程结果: {auto_login_result}")
                
            else:
                print(f"❌ 验证码识别失败: {recognition_result['message']}")
                
                # 手动输入验证码进行测试
                manual_code = input("请手动输入验证码: ").strip()
                if manual_code:
                    print(f"使用手动输入的验证码 '{manual_code}' 进行登录...")
                    login_result = await eir_service.login(manual_code)
                    print(f"手动登录结果: {login_result}")
        else:
            print(f"❌ 验证码获取失败: {result['message']}")
        
        # 清理资源
        eir_service.close()
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def test_captcha_service_only():
    """仅测试验证码识别服务"""
    print("=== 验证码识别服务测试 ===")
    
    try:
        from backend.app.services.captcha_service import captcha_service
        
        # 检查是否有现有的验证码图片
        captcha_dir = "data/captcha"
        if os.path.exists(captcha_dir):
            captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
            if captcha_files:
                # 使用最新的验证码文件
                latest_captcha = max(captcha_files, key=lambda x: os.path.getctime(os.path.join(captcha_dir, x)))
                captcha_path = os.path.join(captcha_dir, latest_captcha)
                
                print(f"使用现有验证码文件: {captcha_path}")
                
                # 测试识别
                result = captcha_service.recognize_captcha(captcha_path)
                print(f"识别结果: {result}")
                
                if result["success"]:
                    print(f"✅ 识别成功: {result['best_result']} (置信度: {result['confidence']})")
                    print(f"所有结果: {result['all_results']}")
                else:
                    print(f"❌ 识别失败: {result['message']}")
                
                # 保存预处理图像用于调试
                captcha_service.save_processed_image(captcha_path)
                print("预处理图像已保存到 data/captcha/processed/ 目录")
                
            else:
                print("❌ 没有找到验证码图片文件")
        else:
            print("❌ 验证码目录不存在")
            
    except Exception as e:
        print(f"❌ 验证码服务测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始验证码自动识别测试...")
    
    choice = input("选择测试模式:\n1. 完整测试 (获取验证码 + 识别 + 登录)\n2. 仅测试验证码识别服务\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        asyncio.run(test_captcha_recognition())
    elif choice == "2":
        asyncio.run(test_captcha_service_only())
    else:
        print("无效选择，默认运行完整测试")
        asyncio.run(test_captcha_recognition())

if __name__ == "__main__":
    main()
