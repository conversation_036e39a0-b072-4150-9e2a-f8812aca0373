# 物流运输自动化项目

## 项目概述
这是用于物流运输的一个项目，主要包括打单、订单管理、派车、最后财务对账。

## 项目进展

### ✅ 已完成功能
1. **EIR自动登录系统**
   - 机器学习验证码识别 (98.3%训练准确率)
   - 自动表单填写和登录
   - 登录状态验证和Cookie管理
   - 成功登录到EIR系统主页

### 🔄 当前开发
2. **EIR打单功能**
   - 页面：【业务办理】->【出口EIR缴费并打单】
   - 流程：选择船公司 -> 输入订舱号 -> 查询 -> 选择提柜地点 -> 缴费 -> 确认支付
   - 要求：自动完成打单、无纸化派单、下载提柜单PDF

### 📋 待开发功能
3. **订单管理系统**
4. **派车管理系统**
5. **财务对账系统**

## 系统账号

### EIR打单页面
- 地址：https://eir.cmclink.com
- 账号：Boshanwl
- 密码：BOS,./

### 约柜页面
- 地址：https://dpay.cmclink.com
- 账号：Boshanwl
- 密码：BOS,./

## 打单流程详细步骤

1. **登录EIR系统** ✅
   - 自动验证码识别
   - 表单填写和提交
   - 登录状态确认

2. **导航到打单页面** 🔄
   - 点击【业务办理】菜单
   - 选择【出口EIR缴费并打单】

3. **填写打单信息** 🔄
   - 选择Carrier(船公司)
   - 输入订舱号码(S/O)
   - 点击【查询】

4. **处理查询结果** 🔄
   - 显示单据地点
   - 选择提柜地点(优先选择码头而非堆场)
   - 点击【缴费】

5. **完成支付流程** 🔄
   - 点击【确认支付】
   - 下载提柜单PDF
   - 完成无纸化派单

## 技术架构

### 核心组件
- **验证码识别**: 机器学习模型 (Random Forest)
- **浏览器自动化**: Selenium WebDriver
- **页面分析**: 动态元素定位和表单处理
- **文件管理**: PDF下载和存储

### 项目文件结构
```
├── backend/
│   ├── app/
│   │   ├── services/
│   │   │   ├── captcha_service_ml.py     # 机器学习验证码识别
│   │   │   ├── eir_service.py            # EIR服务接口
│   │   │   └── captcha_trainer.py       # 验证码模型训练
│   │   ├── automation/
│   │   │   └── eir_automation.py        # EIR自动化核心
│   │   └── models/
│   │       └── database.py              # 数据库管理
│   └── config.py                        # 配置文件
├── data/
│   ├── captcha_collection/              # 验证码样本
│   ├── captcha_labeled/                 # 标注数据
│   └── models/                          # 训练模型
├── eir_auto_login.py                    # 自动登录脚本
├── eir_order_automation.py              # 打单自动化脚本
└── 项目需求                             # 项目文档
```