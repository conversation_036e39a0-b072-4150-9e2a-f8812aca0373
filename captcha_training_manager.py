#!/usr/bin/env python3
"""
验证码训练数据管理器
用于批量收集验证码、标注数据和准备训练集
"""

import sys
import os
import asyncio
import argparse

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def collect_captchas(target_count: int = 1000, batch_size: int = 50):
    """收集验证码"""
    print(f"🚀 开始收集验证码，目标数量: {target_count}")
    
    try:
        from backend.app.services.captcha_collector import captcha_collector
        
        result = captcha_collector.collect_captchas(target_count, batch_size)
        
        print(f"\n📊 收集结果:")
        print(f"会话ID: {result.get('session_id')}")
        print(f"状态: {result.get('status')}")
        print(f"成功收集: {result.get('success_count', 0)} 个")
        print(f"失败: {result.get('failed_count', 0)} 个")
        print(f"总计尝试: {result.get('collected_count', 0)} 个")
        
        if result.get('status') == 'completed':
            print("✅ 验证码收集完成！")
        else:
            print(f"⚠️  收集未完成: {result.get('error', '未知错误')}")
        
        # 显示统计信息
        stats = captcha_collector.get_collection_statistics()
        print(f"\n📈 总体统计:")
        print(f"累计收集: {stats.get('total_collected', 0)} 个验证码")
        print(f"收集会话: {stats.get('sessions_count', 0)} 次")
        
        return result
        
    except Exception as e:
        print(f"❌ 收集过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def label_captchas(batch_size: int = 50):
    """标注验证码"""
    print(f"🏷️  开始标注验证码，批次大小: {batch_size}")
    
    try:
        from backend.app.services.captcha_labeler import captcha_labeler
        
        # 显示当前统计
        stats = captcha_labeler.get_labeling_statistics()
        print(f"\n📊 当前状态:")
        print(f"总收集数量: {stats['total_collected']}")
        print(f"已标注数量: {stats['labeled_count']}")
        print(f"未标注数量: {stats['unlabeled_count']}")
        print(f"标注进度: {stats['labeling_progress']:.1f}%")
        
        if stats['unlabeled_count'] == 0:
            print("✅ 所有验证码都已标注完成！")
            return
        
        # 开始交互式标注
        result = captcha_labeler.batch_label_interactive(batch_size)
        
        print(f"\n📊 标注结果:")
        print(f"会话ID: {result.get('session_id')}")
        print(f"状态: {result.get('status')}")
        print(f"已标注: {result.get('labeled_count', 0)} 个")
        print(f"跳过: {result.get('skipped_count', 0)} 个")
        
        if result.get('status') == 'completed':
            print("✅ 标注完成！")
        elif result.get('status') == 'interrupted':
            print("⚠️  标注被中断")
        else:
            print(f"❌ 标注失败: {result.get('error', '未知错误')}")
        
        return result
        
    except Exception as e:
        print(f"❌ 标注过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_statistics():
    """显示统计信息"""
    print("📊 验证码训练数据统计")
    
    try:
        from backend.app.services.captcha_collector import captcha_collector
        from backend.app.services.captcha_labeler import captcha_labeler
        
        # 收集统计
        collection_stats = captcha_collector.get_collection_statistics()
        print(f"\n🗂️  收集统计:")
        print(f"累计收集: {collection_stats.get('total_collected', 0)} 个验证码")
        print(f"收集会话: {collection_stats.get('sessions_count', 0)} 次")
        print(f"成功率: {collection_stats.get('statistics', {}).get('success_count', 0)} / {collection_stats.get('statistics', {}).get('success_count', 0) + collection_stats.get('statistics', {}).get('failed_count', 0)}")
        
        if 'file_size_stats' in collection_stats:
            fs = collection_stats['file_size_stats']
            print(f"文件大小: {fs['min']}-{fs['max']} bytes (平均: {fs['mean']:.0f})")
        
        if 'image_stats' in collection_stats:
            img = collection_stats['image_stats']
            print(f"图片尺寸: {img['width_range'][0]}-{img['width_range'][1]} x {img['height_range'][0]}-{img['height_range'][1]}")
            print(f"常见尺寸: {img['common_size']}")
        
        # 标注统计
        labeling_stats = captcha_labeler.get_labeling_statistics()
        print(f"\n🏷️  标注统计:")
        print(f"已标注: {labeling_stats['labeled_count']} 个")
        print(f"未标注: {labeling_stats['unlabeled_count']} 个")
        print(f"标注进度: {labeling_stats['labeling_progress']:.1f}%")
        print(f"标注会话: {labeling_stats['sessions_count']} 次")
        
        print(f"\n🔢 数字频率分布:")
        for digit, count in labeling_stats['digit_frequency'].items():
            if count > 0:
                print(f"  {digit}: {count} 次")
        
        print(f"\n📏 长度分布:")
        for length, count in labeling_stats['length_distribution'].items():
            print(f"  {length}位: {count} 个")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def export_training_data():
    """导出训练数据"""
    print("📦 导出训练数据")
    
    try:
        from backend.app.services.captcha_labeler import captcha_labeler
        
        result = captcha_labeler.export_training_data()
        
        if result.get('status') == 'success':
            print(f"✅ 训练数据导出成功！")
            print(f"文件路径: {result.get('training_data_file')}")
            print(f"样本数量: {result.get('sample_count')}")
            print(f"导出时间: {result.get('export_time')}")
        else:
            print(f"❌ 导出失败: {result.get('error')}")
        
        return result
        
    except Exception as e:
        print(f"❌ 导出过程发生错误: {e}")
        return None

def quick_collect_and_label(count: int = 100):
    """快速收集和标注流程"""
    print(f"⚡ 快速收集和标注 {count} 个验证码")
    
    # 1. 收集验证码
    print("\n第1步: 收集验证码")
    collect_result = collect_captchas(count, min(50, count))
    
    if not collect_result or collect_result.get('status') != 'completed':
        print("❌ 收集失败，无法继续标注")
        return
    
    # 2. 标注验证码
    print("\n第2步: 标注验证码")
    label_result = label_captchas(count)
    
    # 3. 导出训练数据
    print("\n第3步: 导出训练数据")
    export_result = export_training_data()
    
    # 4. 显示最终统计
    print("\n第4步: 最终统计")
    show_statistics()
    
    print(f"\n🎉 快速流程完成！")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="验证码训练数据管理器")
    parser.add_argument("action", choices=["collect", "label", "stats", "export", "quick"], 
                       help="操作类型")
    parser.add_argument("--count", type=int, default=1000, 
                       help="收集数量 (默认: 1000)")
    parser.add_argument("--batch", type=int, default=50, 
                       help="批次大小 (默认: 50)")
    
    args = parser.parse_args()
    
    print("🎯 验证码训练数据管理器")
    print("=" * 50)
    
    if args.action == "collect":
        collect_captchas(args.count, args.batch)
    
    elif args.action == "label":
        label_captchas(args.batch)
    
    elif args.action == "stats":
        show_statistics()
    
    elif args.action == "export":
        export_training_data()
    
    elif args.action == "quick":
        quick_collect_and_label(args.count)
    
    print("\n✅ 操作完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
