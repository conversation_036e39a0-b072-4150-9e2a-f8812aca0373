#!/usr/bin/env python3
"""
ChromeDriver自动安装脚本
"""

import os
import sys
import requests
import zipfile
import json
import subprocess
from pathlib import Path

def get_chrome_version():
    """获取Chrome版本"""
    try:
        # Windows方法1: 通过注册表
        import winreg
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
        version, _ = winreg.QueryValueEx(key, "version")
        winreg.CloseKey(key)
        return version
    except:
        pass
    
    try:
        # Windows方法2: 通过文件版本
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                # 使用PowerShell获取文件版本
                cmd = f'(Get-Item "{path}").VersionInfo.FileVersion'
                result = subprocess.run(["powershell", "-Command", cmd], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    if version:
                        return version
    except:
        pass
    
    return None

def get_chromedriver_version(chrome_version):
    """根据Chrome版本获取对应的ChromeDriver版本"""
    if not chrome_version:
        return None
    
    major_version = chrome_version.split('.')[0]
    
    try:
        # 对于Chrome 115+，使用新的API
        if int(major_version) >= 115:
            url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            # 查找匹配的版本
            for version_info in reversed(data['versions']):
                if version_info['version'].startswith(major_version + '.'):
                    downloads = version_info.get('downloads', {})
                    chromedriver = downloads.get('chromedriver', [])
                    for download in chromedriver:
                        if download['platform'] == 'win64':
                            return version_info['version'], download['url']
        else:
            # 对于旧版本Chrome，使用旧的API
            url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                driver_version = response.text.strip()
                download_url = f"https://chromedriver.storage.googleapis.com/{driver_version}/chromedriver_win32.zip"
                return driver_version, download_url
    except Exception as e:
        print(f"获取ChromeDriver版本失败: {e}")
    
    return None, None

def download_chromedriver(url, version):
    """下载ChromeDriver"""
    print(f"正在下载ChromeDriver {version}...")
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # 保存到临时文件
        zip_path = "chromedriver.zip"
        with open(zip_path, "wb") as f:
            f.write(response.content)
        
        print("下载完成，正在解压...")
        
        # 解压文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(".")
        
        # 删除zip文件
        os.remove(zip_path)
        
        # 查找chromedriver.exe
        chromedriver_paths = [
            "chromedriver.exe",
            "chromedriver-win64/chromedriver.exe",
            "chromedriver-win32/chromedriver.exe"
        ]
        
        for path in chromedriver_paths:
            if os.path.exists(path):
                # 移动到项目根目录
                final_path = "chromedriver.exe"
                if path != final_path:
                    if os.path.exists(final_path):
                        os.remove(final_path)
                    os.rename(path, final_path)
                    # 清理目录
                    import shutil
                    for dir_name in ["chromedriver-win64", "chromedriver-win32"]:
                        if os.path.exists(dir_name):
                            shutil.rmtree(dir_name)
                
                print(f"✅ ChromeDriver安装成功: {os.path.abspath(final_path)}")
                return True
        
        print("❌ 解压后未找到chromedriver.exe")
        return False
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def test_chromedriver():
    """测试ChromeDriver是否工作"""
    try:
        if not os.path.exists("chromedriver.exe"):
            print("❌ chromedriver.exe不存在")
            return False
        
        # 测试ChromeDriver版本
        result = subprocess.run(["chromedriver.exe", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ChromeDriver测试成功: {version}")
            return True
        else:
            print(f"❌ ChromeDriver测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ChromeDriver测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=== ChromeDriver自动安装工具 ===")
    
    # 1. 检查Chrome版本
    print("\n1. 检查Chrome版本...")
    chrome_version = get_chrome_version()
    
    if not chrome_version:
        print("❌ 未检测到Chrome浏览器")
        print("请先安装Chrome浏览器:")
        print("下载地址: https://www.google.com/chrome/")
        return False
    
    print(f"✅ 检测到Chrome版本: {chrome_version}")
    
    # 2. 获取对应的ChromeDriver版本
    print("\n2. 查找对应的ChromeDriver版本...")
    driver_version, download_url = get_chromedriver_version(chrome_version)
    
    if not driver_version:
        print("❌ 未找到对应的ChromeDriver版本")
        print("请手动下载: https://chromedriver.chromium.org/")
        return False
    
    print(f"✅ 找到ChromeDriver版本: {driver_version}")
    
    # 3. 检查是否已存在
    if os.path.exists("chromedriver.exe"):
        print("\n3. 检查现有ChromeDriver...")
        if test_chromedriver():
            choice = input("ChromeDriver已存在且可用，是否重新下载？(y/n): ").strip().lower()
            if choice != 'y':
                print("✅ 使用现有ChromeDriver")
                return True
        else:
            print("现有ChromeDriver不可用，将重新下载")
    
    # 4. 下载ChromeDriver
    print(f"\n4. 下载ChromeDriver...")
    if not download_chromedriver(download_url, driver_version):
        return False
    
    # 5. 测试ChromeDriver
    print("\n5. 测试ChromeDriver...")
    if not test_chromedriver():
        return False
    
    print("\n🎉 ChromeDriver安装完成！")
    print("现在可以运行完整的EIR登录测试了")
    print("运行命令: python test_login.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 安装失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1)
