# 物流运输管理系统 - EIR登录功能

这是一个物流运输管理系统的第一阶段实现，主要完成EIR系统的自动登录功能，包括验证码处理和Cookie保存。

## 功能特性

- ✅ EIR系统自动登录
- ✅ 图片验证码处理
- ✅ 登录状态保持（Cookie管理）
- ✅ SQLite数据库存储
- ✅ Web界面测试
- ✅ 操作日志记录

## 项目结构

```
BS/
├── backend/                    # 后端服务
│   ├── app/
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── models/
│   │   │   └── database.py    # 数据库模型
│   │   ├── services/
│   │   │   └── eir_service.py # EIR业务服务
│   │   └── automation/
│   │       └── eir_automation.py # EIR自动化脚本
│   └── config.py              # 配置文件
├── frontend/
│   └── index.html             # 测试界面
├── data/                      # 数据目录
│   ├── captcha/              # 验证码图片
│   └── cookies/              # Cookie存储
├── requirements.txt           # Python依赖
├── start_server.py           # 启动脚本
├── test_login.py             # 命令行测试脚本
└── logistics.db              # SQLite数据库
```

## 安装要求

### 系统要求
- Python 3.8+
- Chrome浏览器
- ChromeDriver

### Python依赖
```
fastapi==0.104.1
uvicorn==0.24.0
selenium==4.15.2
requests==2.31.0
Pillow==10.1.0
python-multipart==0.0.6
aiofiles==23.2.1
```

## 快速开始

### 1. 安装依赖

```bash
# 激活虚拟环境（如果有）
.venv\Scripts\activate  # Windows
# 或
source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt
```

### 2. 安装ChromeDriver

下载并安装ChromeDriver：
- 下载地址：https://chromedriver.chromium.org/
- 确保ChromeDriver在系统PATH中，或放在项目目录下

### 3. 启动服务

```bash
# 方式1：使用启动脚本（推荐）
python start_server.py

# 方式2：直接启动API服务
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000
```

### 4. 测试登录功能

#### Web界面测试
1. 启动服务后会自动打开浏览器
2. 或手动打开 `frontend/index.html`
3. 点击"获取验证码"
4. 输入验证码并点击"登录"

#### 命令行测试
```bash
python test_login.py
```

## API接口

### 获取验证码
```
GET /api/eir/captcha
```

### 刷新验证码
```
POST /api/eir/captcha/refresh
```

### 登录
```
POST /api/eir/login
Content-Type: application/json

{
    "captcha_code": "1234"
}
```

### 检查登录状态
```
GET /api/eir/status
```

## 配置说明

EIR登录信息在 `backend/config.py` 中配置：

```python
# EIR配置
EIR_BASE_URL = "https://eir.cmclink.com"
EIR_USERNAME = "Boshanwl"
EIR_PASSWORD = "BOS,./"
```

## 数据存储

系统使用SQLite数据库存储以下信息：

1. **login_sessions** - 登录会话和Cookie
2. **orders** - 订单信息
3. **operation_logs** - 操作日志

## 功能说明

### 1. 验证码处理
- 自动下载验证码图片
- 支持验证码刷新
- 图片保存在 `data/captcha/` 目录

### 2. Cookie管理
- 登录成功后自动保存Cookie
- 下次访问时优先使用保存的Cookie
- Cookie失效时自动重新登录

### 3. 错误处理
- 网络超时处理
- 验证码错误重试
- 详细的错误日志记录

## 故障排除

### 1. ChromeDriver问题
```
selenium.common.exceptions.WebDriverException: 'chromedriver' executable needs to be in PATH
```
**解决方案：**
- 下载对应Chrome版本的ChromeDriver
- 将ChromeDriver添加到系统PATH
- 或将ChromeDriver放在项目根目录

### 2. 验证码获取失败
**可能原因：**
- 网络连接问题
- EIR网站访问限制
- Chrome浏览器启动失败

**解决方案：**
- 检查网络连接
- 确认EIR网站可正常访问
- 重启服务

### 3. 登录失败
**可能原因：**
- 验证码输入错误
- 账号密码错误
- Cookie过期

**解决方案：**
- 刷新验证码重试
- 检查账号密码配置
- 清除保存的Cookie重新登录

## 下一步计划

1. **打单功能**：实现自动填写订单信息并提交
2. **PDF下载**：自动下载生成的提柜单PDF
3. **约柜功能**：集成约柜系统自动化
4. **批量处理**：支持批量订单处理
5. **用户界面**：完善的前端管理界面

## 技术支持

如有问题，请查看：
1. 操作日志：数据库中的 `operation_logs` 表
2. 控制台输出：启动服务时的详细日志
3. 验证码图片：`data/captcha/` 目录下的图片文件

## 注意事项

1. 请确保EIR账号密码正确
2. 验证码有时效性，请及时输入
3. 系统会自动保存登录状态，无需重复登录
4. 建议在稳定的网络环境下使用
