#!/usr/bin/env python3
"""
验证码模型训练脚本
"""

import sys
import os
import argparse

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def train_models():
    """训练验证码识别模型"""
    print("🤖 开始训练验证码识别模型...")
    
    try:
        from backend.app.services.captcha_trainer import captcha_trainer
        
        result = captcha_trainer.train_models()
        
        print(f"\n📊 训练结果:")
        print(f"状态: {result.get('status')}")
        
        if result.get('status') == 'success':
            print(f"✅ 模型训练成功！")
            print(f"训练样本数: {result.get('sample_count')}")
            print(f"特征维度: {result.get('feature_dim')}")
            print(f"分位置模型平均准确率: {result.get('average_digit_accuracy', 0):.3f}")
            print(f"整体模型准确率: {result.get('whole_model_accuracy', 0):.3f}")
            
            print(f"\n📁 模型文件:")
            print(f"模型目录: data/models/")
            print(f"- digit_model_position_0.joblib (第1位数字)")
            print(f"- digit_model_position_1.joblib (第2位数字)")
            print(f"- digit_model_position_2.joblib (第3位数字)")
            print(f"- digit_model_position_3.joblib (第4位数字)")
            print(f"- whole_captcha_model.joblib (整体模型)")
            print(f"- training_info.json (训练信息)")
            
        else:
            print(f"❌ 训练失败: {result.get('error', '未知错误')}")
        
        return result
        
    except Exception as e:
        print(f"❌ 训练过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_models(image_path: str = None):
    """测试训练好的模型"""
    print("🧪 测试验证码识别模型...")
    
    try:
        from backend.app.services.captcha_trainer import captcha_trainer
        
        # 加载模型
        models = captcha_trainer.load_trained_models()
        if not models:
            print("❌ 没有找到训练好的模型")
            return
        
        print(f"✅ 加载了模型:")
        for key in models.keys():
            if key != "training_info":
                print(f"  - {key}")
        
        # 如果没有指定图片，使用收集的验证码进行测试
        if not image_path:
            # 查找测试图片
            captcha_dir = "data/captcha_collection"
            if os.path.exists(captcha_dir):
                captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
                if captcha_files:
                    image_path = os.path.join(captcha_dir, captcha_files[0])
                    print(f"使用测试图片: {image_path}")
        
        if not image_path or not os.path.exists(image_path):
            print("❌ 没有找到测试图片")
            return
        
        # 进行预测
        result = captcha_trainer.predict_captcha(image_path, models)
        
        print(f"\n🔮 预测结果:")
        if result.get('status') == 'success':
            print(f"最佳预测: {result.get('best_prediction')}")
            print(f"置信度: {result.get('best_confidence', 0):.3f}")
            
            print(f"\n详细预测:")
            for model_name, pred_info in result.get('all_predictions', {}).items():
                print(f"  {model_name}: {pred_info.get('prediction')} (置信度: {pred_info.get('confidence', 0):.3f})")
        else:
            print(f"❌ 预测失败: {result.get('message')}")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_model_info():
    """显示模型信息"""
    print("📋 模型信息")
    
    try:
        from backend.app.services.captcha_trainer import captcha_trainer
        
        models = captcha_trainer.load_trained_models()
        
        if "training_info" in models:
            info = models["training_info"]
            print(f"\n🕐 训练时间: {info.get('training_time')}")
            print(f"📊 训练样本数: {info.get('sample_count')}")
            print(f"🔢 特征维度: {info.get('feature_dim')}")
            
            print(f"\n📈 分位置模型性能:")
            for pos, model_info in info.get('models', {}).items():
                print(f"  {pos}: 准确率 {model_info.get('accuracy', 0):.3f} "
                      f"(训练: {model_info.get('train_samples')}, 测试: {model_info.get('test_samples')})")
        
        # 检查模型文件
        models_dir = "data/models"
        if os.path.exists(models_dir):
            model_files = [f for f in os.listdir(models_dir) if f.endswith('.joblib')]
            print(f"\n📁 模型文件 ({len(model_files)} 个):")
            for file in sorted(model_files):
                file_path = os.path.join(models_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"  {file} ({file_size:,} bytes)")
        
    except Exception as e:
        print(f"❌ 获取模型信息失败: {e}")

def batch_test_models():
    """批量测试模型性能"""
    print("🔬 批量测试模型性能...")
    
    try:
        from backend.app.services.captcha_trainer import captcha_trainer
        from backend.app.services.captcha_labeler import captcha_labeler
        
        # 加载模型
        models = captcha_trainer.load_trained_models()
        if not models:
            print("❌ 没有找到训练好的模型")
            return
        
        # 获取标注数据
        labels_data = captcha_labeler.labels.get("labels", {})
        if not labels_data:
            print("❌ 没有找到标注数据")
            return
        
        print(f"📊 测试 {len(labels_data)} 个标注样本...")
        
        correct_predictions = 0
        total_predictions = 0
        
        for filename, label_info in list(labels_data.items())[:50]:  # 测试前50个
            # 查找图片文件
            captcha_dir = "data/captcha_collection"
            image_path = os.path.join(captcha_dir, filename)
            
            if not os.path.exists(image_path):
                continue
            
            true_label = label_info.get("label")
            if not true_label:
                continue
            
            # 预测
            result = captcha_trainer.predict_captcha(image_path, models)
            if result.get('status') == 'success':
                predicted_label = result.get('best_prediction')
                
                if predicted_label == true_label:
                    correct_predictions += 1
                
                total_predictions += 1
                
                if total_predictions <= 10:  # 显示前10个结果
                    status = "✅" if predicted_label == true_label else "❌"
                    print(f"  {status} {filename}: 真实={true_label}, 预测={predicted_label}")
        
        if total_predictions > 0:
            accuracy = correct_predictions / total_predictions
            print(f"\n📈 批量测试结果:")
            print(f"测试样本数: {total_predictions}")
            print(f"正确预测: {correct_predictions}")
            print(f"准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
        else:
            print("❌ 没有有效的测试样本")
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="验证码模型训练器")
    parser.add_argument("action", choices=["train", "test", "info", "batch_test"], 
                       help="操作类型")
    parser.add_argument("--image", type=str, 
                       help="测试图片路径")
    
    args = parser.parse_args()
    
    print("🤖 验证码模型训练器")
    print("=" * 50)
    
    if args.action == "train":
        train_models()
    
    elif args.action == "test":
        test_models(args.image)
    
    elif args.action == "info":
        show_model_info()
    
    elif args.action == "batch_test":
        batch_test_models()
    
    print("\n✅ 操作完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
