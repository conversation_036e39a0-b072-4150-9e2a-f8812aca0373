#!/usr/bin/env python3
"""
简化版GUI验证码标注工具
确保兼容性和稳定性
"""

import sys
import os
import json
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class SimpleCaptchaLabeler:
    def __init__(self):
        # 初始化数据
        self.collection_dir = "data/captcha_collection"
        self.labels_file = "data/captcha_labeled/labels.json"
        self.labels_dir = "data/captcha_labeled"
        
        # 创建标注目录
        os.makedirs(self.labels_dir, exist_ok=True)
        
        # 加载数据
        self.labels = self.load_labels()
        self.unlabeled_files = self.get_unlabeled_files()
        self.current_index = 0
        
        # 创建GUI
        self.root = tk.Tk()
        self.root.title("验证码标注工具")
        self.root.geometry("500x400")
        
        # 创建界面
        self.create_widgets()
        self.load_current_image()
        
        # 绑定事件
        self.root.bind('<Return>', lambda e: self.submit_label())
        self.root.bind('<Escape>', lambda e: self.skip_current())
        
    def load_labels(self):
        """加载标注数据"""
        if os.path.exists(self.labels_file):
            try:
                with open(self.labels_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "labeled_count": 0,
            "labels": {},
            "statistics": {
                "digit_frequency": {str(i): 0 for i in range(10)}
            }
        }
    
    def save_labels(self):
        """保存标注数据"""
        try:
            with open(self.labels_file, 'w', encoding='utf-8') as f:
                json.dump(self.labels, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")
            return False
    
    def get_unlabeled_files(self):
        """获取未标注的文件列表"""
        if not os.path.exists(self.collection_dir):
            return []
        
        all_files = [f for f in os.listdir(self.collection_dir) if f.endswith('.png')]
        labeled_files = set(self.labels.get("labels", {}).keys())
        
        unlabeled = [f for f in all_files if f not in labeled_files]
        return sorted(unlabeled)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🏷️ 验证码标注工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 统计信息
        self.stats_var = tk.StringVar()
        stats_label = ttk.Label(main_frame, textvariable=self.stats_var)
        stats_label.pack(pady=(0, 10))
        
        # 图片信息区域
        image_frame = ttk.LabelFrame(main_frame, text="验证码信息", padding="10")
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件名
        self.filename_var = tk.StringVar()
        filename_label = ttk.Label(image_frame, textvariable=self.filename_var, font=("Arial", 10))
        filename_label.pack()
        
        # 图片路径（可点击打开）
        self.filepath_var = tk.StringVar()
        filepath_label = ttk.Label(image_frame, textvariable=self.filepath_var, 
                                 font=("Arial", 9), foreground="blue", cursor="hand2")
        filepath_label.pack()
        filepath_label.bind("<Button-1>", self.open_image_file)
        
        # ASCII预览
        self.ascii_var = tk.StringVar()
        ascii_label = ttk.Label(image_frame, textvariable=self.ascii_var, 
                               font=("Courier", 8), justify=tk.CENTER)
        ascii_label.pack(pady=(5, 0))
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="输入验证码", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输入框
        input_container = ttk.Frame(input_frame)
        input_container.pack()
        
        ttk.Label(input_container, text="验证码:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.entry_var = tk.StringVar()
        self.entry = ttk.Entry(input_container, textvariable=self.entry_var, 
                              font=("Arial", 14), width=8)
        self.entry.pack(side=tk.LEFT)
        self.entry.focus_set()
        
        # 输入验证
        self.entry_var.trace('w', self.validate_input)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.submit_btn = ttk.Button(button_frame, text="✅ 提交 (Enter)", 
                                   command=self.submit_label, state="disabled")
        self.submit_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="⏭️ 跳过 (Esc)", 
                  command=self.skip_current).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="📁 打开图片", 
                  command=self.open_current_image).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="💾 保存", 
                  command=self.save_labels).pack(side=tk.RIGHT)
        
        # 进度信息
        self.progress_var = tk.StringVar()
        progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        progress_label.pack()
        
        self.update_stats()
    
    def validate_input(self, *args):
        """验证输入"""
        text = self.entry_var.get()
        
        # 只允许数字，最多4位
        if text and not text.isdigit():
            self.entry_var.set(''.join(c for c in text if c.isdigit()))
        
        if len(text) > 4:
            self.entry_var.set(text[:4])
        
        # 更新提交按钮状态
        if len(self.entry_var.get()) == 4:
            self.submit_btn.configure(state="normal")
        else:
            self.submit_btn.configure(state="disabled")
    
    def load_current_image(self):
        """加载当前图片信息"""
        if not self.unlabeled_files or self.current_index >= len(self.unlabeled_files):
            self.show_completion()
            return
        
        filename = self.unlabeled_files[self.current_index]
        filepath = os.path.join(self.collection_dir, filename)
        
        # 更新显示信息
        self.filename_var.set(f"文件: {filename}")
        self.filepath_var.set(f"路径: {filepath}")
        
        # 生成ASCII预览
        ascii_preview = self.generate_ascii_preview(filepath)
        self.ascii_var.set(ascii_preview)
        
        # 更新进度
        self.update_progress()
        
        # 清空输入框
        self.entry_var.set("")
        self.entry.focus_set()
    
    def generate_ascii_preview(self, filepath):
        """生成ASCII预览"""
        try:
            import cv2
            import numpy as np
            
            img = cv2.imread(filepath, cv2.IMREAD_GRAYSCALE)
            if img is None:
                return "无法读取图片"
            
            # 调整尺寸
            height, width = img.shape
            new_width = 30
            new_height = int(height * new_width / width * 0.5)
            resized = cv2.resize(img, (new_width, new_height))
            
            # 二值化
            _, binary = cv2.threshold(resized, 127, 255, cv2.THRESH_BINARY)
            
            # 转换为ASCII
            ascii_art = ""
            for row in binary:
                line = ""
                for pixel in row:
                    if pixel < 128:
                        line += "█"
                    else:
                        line += " "
                ascii_art += line + "\n"
            
            return ascii_art
            
        except Exception as e:
            return f"预览生成失败: {e}"
    
    def open_image_file(self, event=None):
        """打开图片文件"""
        if self.unlabeled_files and self.current_index < len(self.unlabeled_files):
            filename = self.unlabeled_files[self.current_index]
            filepath = os.path.join(self.collection_dir, filename)
            
            try:
                import subprocess
                subprocess.run(['start', filepath], shell=True, check=True)
            except:
                messagebox.showinfo("提示", f"请手动打开文件:\n{filepath}")
    
    def open_current_image(self):
        """打开当前图片"""
        self.open_image_file()
    
    def update_stats(self):
        """更新统计信息"""
        labeled_count = len(self.labels.get("labels", {}))
        total_count = labeled_count + len(self.unlabeled_files)
        
        if total_count > 0:
            progress_pct = labeled_count / total_count * 100
            stats_text = f"已标注: {labeled_count} | 待标注: {len(self.unlabeled_files)} | 进度: {progress_pct:.1f}%"
        else:
            stats_text = "没有验证码数据"
        
        self.stats_var.set(stats_text)
    
    def update_progress(self):
        """更新进度信息"""
        total = len(self.unlabeled_files)
        current = self.current_index + 1
        
        if total > 0:
            progress_text = f"当前: {current}/{total} ({current/total*100:.1f}%)"
        else:
            progress_text = "没有待标注的图片"
        
        self.progress_var.set(progress_text)
    
    def submit_label(self):
        """提交标注"""
        label = self.entry_var.get().strip()
        
        if len(label) != 4 or not label.isdigit():
            messagebox.showwarning("输入错误", "请输入4位数字")
            return
        
        if not self.unlabeled_files or self.current_index >= len(self.unlabeled_files):
            return
        
        filename = self.unlabeled_files[self.current_index]
        
        # 添加标注
        self.labels["labels"][filename] = {
            "label": label,
            "labeled_time": datetime.now().isoformat()
        }
        
        # 更新统计
        self.labels["labeled_count"] = len(self.labels["labels"])
        
        for digit in label:
            if digit in self.labels["statistics"]["digit_frequency"]:
                self.labels["statistics"]["digit_frequency"][digit] += 1
        
        # 保存数据
        self.save_labels()
        
        # 从未标注列表中移除
        self.unlabeled_files.pop(self.current_index)
        
        # 更新统计显示
        self.update_stats()
        
        # 加载下一张图片
        if self.current_index >= len(self.unlabeled_files):
            self.current_index = max(0, len(self.unlabeled_files) - 1)
        
        self.load_current_image()
        
        # 显示成功消息
        self.root.title(f"验证码标注工具 - 已标注: {label}")
        self.root.after(2000, lambda: self.root.title("验证码标注工具"))
    
    def skip_current(self):
        """跳过当前验证码"""
        if self.unlabeled_files and self.current_index < len(self.unlabeled_files) - 1:
            self.current_index += 1
            self.load_current_image()
    
    def show_completion(self):
        """显示完成信息"""
        self.filename_var.set("🎉 所有验证码已标注完成！")
        self.filepath_var.set("可以开始训练模型了")
        self.ascii_var.set("")
        self.progress_var.set("进度: 100%")
        
        # 禁用相关控件
        self.submit_btn.configure(state="disabled")
        self.entry.configure(state="disabled")
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"GUI运行错误: {e}")

def main():
    """主函数"""
    try:
        print("🚀 启动简化版GUI标注工具...")
        app = SimpleCaptchaLabeler()
        app.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
