#!/usr/bin/env python3
"""
测试集成机器学习模型后的自动登录功能
"""

import sys
import os
import asyncio
import time

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_ml_captcha_service():
    """测试机器学习验证码识别服务"""
    print("🤖 测试机器学习验证码识别服务")
    print("=" * 50)
    
    try:
        from backend.app.services.captcha_service_ml import ml_captcha_service
        
        # 1. 检查模型状态
        print("1. 检查模型加载状态...")
        model_info = ml_captcha_service.get_model_info()
        print(f"模型状态: {model_info}")
        
        if model_info.get("status") != "loaded":
            print("❌ 模型未正确加载")
            return False
        
        print(f"✅ 模型加载成功")
        print(f"   - 分位置模型: {model_info.get('models_count')} 个")
        print(f"   - 整体模型: {'是' if model_info.get('has_whole_model') else '否'}")
        
        # 2. 测试验证码识别
        print("\n2. 测试验证码识别...")
        test_result = ml_captcha_service.test_model()
        
        if test_result.get("status") == "success":
            recognition = test_result.get("recognition_result", {})
            print(f"✅ 识别测试成功")
            print(f"   - 测试图片: {test_result.get('test_image')}")
            print(f"   - 识别结果: {recognition.get('best_result')}")
            print(f"   - 置信度: {recognition.get('confidence', 0):.3f}")
            print(f"   - 候选列表: {recognition.get('all_results', [])}")
            return True
        else:
            print(f"❌ 识别测试失败: {test_result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_eir_service_integration():
    """测试EIR服务集成"""
    print("\n🌐 测试EIR服务集成")
    print("=" * 50)
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务实例...")
        eir_service = EIRService()
        
        print("2. 测试验证码获取...")
        captcha_result = await eir_service.get_login_captcha()
        
        if not captcha_result.get("success"):
            print(f"❌ 验证码获取失败: {captcha_result.get('message')}")
            return False
        
        captcha_path = captcha_result.get("captcha_path")
        print(f"✅ 验证码获取成功: {captcha_path}")
        
        print("3. 测试机器学习验证码识别...")
        recognition_result = await eir_service.recognize_captcha(captcha_path)
        
        if recognition_result.get("success"):
            print(f"✅ 验证码识别成功")
            print(f"   - 识别结果: {recognition_result.get('best_result')}")
            print(f"   - 置信度: {recognition_result.get('confidence', 0):.3f}")
            print(f"   - 识别方法: {recognition_result.get('method')}")
            print(f"   - 候选列表: {recognition_result.get('all_results', [])}")
            
            # 清理资源
            eir_service.close()
            return True
        else:
            print(f"❌ 验证码识别失败: {recognition_result.get('message')}")
            eir_service.close()
            return False
            
    except Exception as e:
        print(f"❌ EIR服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_auto_login():
    """测试完整自动登录流程"""
    print("\n🚀 测试完整自动登录流程")
    print("=" * 50)
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务实例...")
        eir_service = EIRService()
        
        print("2. 开始完整自动登录...")
        print("   - 自动获取验证码")
        print("   - 机器学习识别验证码")
        print("   - 智能重试机制")
        print("   - 多候选尝试")
        
        start_time = time.time()
        result = await eir_service.auto_login()
        end_time = time.time()
        
        print(f"\n自动登录结果: {result}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        if result.get("success"):
            print("🎉 完整自动登录成功！")
            print("✅ 系统已自动完成:")
            print("   - 验证码下载")
            print("   - 机器学习识别 (98.3%准确率)")
            print("   - 登录提交")
            print("   - Cookie保存")
            
            # 验证登录状态
            print("\n3. 验证登录状态...")
            status_result = await eir_service.check_login_status()
            print(f"登录状态: {status_result}")
            
            eir_service.close()
            return True
        else:
            print(f"❌ 自动登录失败: {result.get('message')}")
            print("\n可能的原因:")
            print("1. 网络连接问题")
            print("2. EIR网站结构变化")
            print("3. 账号密码问题")
            print("4. 验证码识别错误（概率较低）")
            
            eir_service.close()
            return False
        
    except Exception as e:
        print(f"❌ 完整自动登录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_login_attempts():
    """测试多次登录尝试"""
    print("\n🔄 测试多次登录尝试")
    print("=" * 50)
    
    success_count = 0
    total_attempts = 3
    
    for i in range(total_attempts):
        print(f"\n第 {i + 1} 次登录尝试:")
        print("-" * 30)
        
        try:
            from backend.app.services.eir_service import EIRService
            from backend.config import Config
            
            Config.create_directories()
            eir_service = EIRService()
            
            result = await eir_service.auto_login()
            
            if result.get("success"):
                print(f"✅ 第 {i + 1} 次登录成功")
                success_count += 1
            else:
                print(f"❌ 第 {i + 1} 次登录失败: {result.get('message')}")
            
            eir_service.close()
            
            # 等待一段时间再进行下次尝试
            if i < total_attempts - 1:
                print("⏳ 等待5秒后进行下次尝试...")
                await asyncio.sleep(5)
                
        except Exception as e:
            print(f"❌ 第 {i + 1} 次尝试异常: {e}")
    
    success_rate = success_count / total_attempts * 100
    print(f"\n📊 多次登录测试结果:")
    print(f"成功次数: {success_count}/{total_attempts}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 自动登录系统表现优秀！")
    elif success_rate >= 60:
        print("✅ 自动登录系统表现良好")
    else:
        print("⚠️ 自动登录系统需要优化")
    
    return success_rate >= 60

def main():
    """主函数"""
    print("🎯 集成机器学习模型的自动登录测试")
    print("=" * 60)
    
    choice = input("""
选择测试模式:
1. 测试机器学习验证码识别服务
2. 测试EIR服务集成
3. 测试完整自动登录流程
4. 测试多次登录尝试 (3次)
5. 完整测试套件 (推荐)
请输入选择 (1-5): """).strip()
    
    if choice == "1":
        asyncio.run(test_ml_captcha_service())
    elif choice == "2":
        asyncio.run(test_eir_service_integration())
    elif choice == "3":
        asyncio.run(test_full_auto_login())
    elif choice == "4":
        asyncio.run(test_multiple_login_attempts())
    elif choice == "5":
        print("🚀 运行完整测试套件...")
        
        # 依次运行所有测试
        tests = [
            ("机器学习服务", test_ml_captcha_service()),
            ("EIR服务集成", test_eir_service_integration()),
            ("完整自动登录", test_full_auto_login()),
            ("多次登录尝试", test_multiple_login_attempts())
        ]
        
        results = []
        for test_name, test_coro in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                result = asyncio.run(test_coro)
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                results.append((test_name, False))
        
        # 显示总结
        print(f"\n{'='*60}")
        print("🎯 测试总结")
        print("=" * 60)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        overall_success = passed / len(results) * 100
        print(f"\n总体通过率: {passed}/{len(results)} ({overall_success:.1f}%)")
        
        if overall_success >= 80:
            print("🎉 集成测试成功！系统已准备就绪！")
        elif overall_success >= 60:
            print("✅ 集成测试基本成功，系统可以使用")
        else:
            print("⚠️ 集成测试需要改进")
    
    else:
        print("无效选择，运行完整自动登录测试")
        asyncio.run(test_full_auto_login())

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
