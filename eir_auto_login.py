#!/usr/bin/env python3
"""
EIR自动登录脚本 - 生产版本
集成高精度机器学习验证码识别
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class EIRAutoLogin:
    def __init__(self):
        self.max_retries = 3
        self.retry_delay = 5
        
    async def login(self):
        """执行自动登录"""
        print("🚀 EIR自动登录系统启动")
        print("=" * 50)
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            from backend.app.services.eir_service import EIRService
            from backend.config import Config
            
            # 创建必要的目录
            Config.create_directories()
            
            # 创建EIR服务
            eir_service = EIRService()
            
            print("✅ 系统初始化完成")
            print("🤖 机器学习模型已加载 (98.3%训练准确率)")
            
            # 执行自动登录
            print("\n🔄 开始自动登录...")
            start_time = time.time()
            
            result = await eir_service.auto_login()
            
            end_time = time.time()
            login_time = end_time - start_time
            
            if result.get("success"):
                print("\n🎉 自动登录成功！")
                print("=" * 50)
                print("✅ 登录状态: 成功")
                print(f"⏱️ 登录耗时: {login_time:.2f} 秒")
                print(f"🍪 Cookie状态: 已保存")
                print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 验证登录状态
                print("\n🔍 验证登录状态...")
                status_result = await eir_service.check_login_status()
                
                if status_result.get("success"):
                    print("✅ 登录状态确认: 已登录")
                else:
                    print("⚠️ 登录状态确认: 需要验证")
                
                eir_service.close()
                return True
            else:
                error_msg = result.get("message", "未知错误")
                print(f"\n❌ 自动登录失败: {error_msg}")
                print(f"⏱️ 尝试耗时: {login_time:.2f} 秒")
                
                eir_service.close()
                return False
                
        except Exception as e:
            print(f"\n❌ 自动登录异常: {e}")
            return False
    
    async def login_with_retry(self):
        """带重试的自动登录"""
        print("🔄 启动重试机制自动登录")
        print("=" * 50)
        
        for attempt in range(self.max_retries):
            print(f"\n第 {attempt + 1} 次登录尝试:")
            print("-" * 30)
            
            success = await self.login()
            
            if success:
                print(f"\n🎊 登录成功！(第 {attempt + 1} 次尝试)")
                return True
            else:
                if attempt < self.max_retries - 1:
                    print(f"⏳ 等待 {self.retry_delay} 秒后重试...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    print(f"\n💔 经过 {self.max_retries} 次尝试后仍然失败")
        
        return False
    
    async def quick_login(self):
        """快速登录（单次尝试）"""
        print("⚡ 快速自动登录")
        print("=" * 30)
        
        return await self.login()

def main():
    """主函数"""
    print("🎯 EIR自动登录系统")
    print("=" * 40)
    
    choice = input("""
选择登录模式:
1. 快速登录 (单次尝试)
2. 标准登录 (3次重试)
3. 持续登录 (直到成功)
请输入选择 (1-3): """).strip()
    
    auto_login = EIRAutoLogin()
    
    if choice == "1":
        print("\n⚡ 执行快速登录...")
        success = asyncio.run(auto_login.quick_login())
    
    elif choice == "2":
        print("\n🔄 执行标准登录...")
        success = asyncio.run(auto_login.login_with_retry())
    
    elif choice == "3":
        print("\n♾️ 执行持续登录...")
        auto_login.max_retries = 10  # 增加重试次数
        success = asyncio.run(auto_login.login_with_retry())
    
    else:
        print("\n无效选择，执行标准登录...")
        success = asyncio.run(auto_login.login_with_retry())
    
    # 显示最终结果
    print("\n" + "=" * 60)
    if success:
        print("🎉 EIR自动登录完成！")
        print("✅ 您现在可以访问EIR系统")
        print("🍪 登录状态已保存，下次访问更快")
    else:
        print("❌ EIR自动登录失败")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 确认账号密码正确")
        print("   3. 稍后重试")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断登录")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
