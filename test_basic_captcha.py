#!/usr/bin/env python3
"""
基础验证码识别测试
"""

import sys
import os
import asyncio

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_basic_captcha():
    """测试基础验证码识别"""
    print("=== 基础验证码识别测试 ===")
    
    try:
        from backend.app.services.captcha_service_basic import basic_captcha_service
        
        # 检查是否有现有的验证码图片
        captcha_dir = "data/captcha"
        if os.path.exists(captcha_dir):
            captcha_files = [f for f in os.listdir(captcha_dir) if f.endswith('.png')]
            if captcha_files:
                # 使用最新的验证码文件
                latest_captcha = max(captcha_files, key=lambda x: os.path.getctime(os.path.join(captcha_dir, x)))
                captcha_path = os.path.join(captcha_dir, latest_captcha)
                
                print(f"使用现有验证码文件: {captcha_path}")
                
                # 测试识别
                result = basic_captcha_service.recognize_captcha(captcha_path)
                print(f"识别结果: {result}")
                
                if result["success"]:
                    print(f"✅ 识别成功: {result['best_result']} (置信度: {result['confidence']})")
                    print(f"所有候选结果: {result['all_results']}")
                    print(f"图像特征: {result.get('features', {})}")
                    
                    # 显示候选列表供用户选择
                    if len(result['all_results']) > 1:
                        print("\n候选验证码列表:")
                        for i, candidate in enumerate(result['all_results'], 1):
                            print(f"{i}. {candidate}")
                        
                        choice = input(f"\n请选择正确的验证码 (1-{len(result['all_results'])}) 或输入正确答案: ").strip()
                        
                        if choice.isdigit() and 1 <= int(choice) <= len(result['all_results']):
                            selected_code = result['all_results'][int(choice) - 1]
                            print(f"您选择了: {selected_code}")
                        elif choice:
                            selected_code = choice
                            print(f"您输入的验证码: {selected_code}")
                        else:
                            selected_code = result['best_result']
                            print(f"使用默认最佳结果: {selected_code}")
                    else:
                        selected_code = result['best_result']
                        user_input = input(f"识别结果是否正确？(回车确认，或输入正确答案): ").strip()
                        if user_input:
                            selected_code = user_input
                            print(f"您输入的正确答案: {selected_code}")
                    
                    # 分析识别准确性
                    if selected_code != result['best_result']:
                        print(f"\n识别准确性分析:")
                        print(f"系统识别: {result['best_result']}")
                        print(f"正确答案: {selected_code}")
                        print(f"准确性: ❌ 错误")
                    else:
                        print(f"✅ 识别准确")
                        
                else:
                    print(f"❌ 识别失败: {result['message']}")
                    
            else:
                print("❌ 没有找到验证码图片文件")
                print("请先运行获取验证码的测试")
        else:
            print("❌ 验证码目录不存在")
            print("请先运行获取验证码的测试")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_with_new_captcha():
    """获取新验证码并测试识别"""
    print("=== 获取新验证码并测试基础识别 ===")
    
    try:
        from backend.app.services.eir_service import EIRService
        from backend.app.services.captcha_service_basic import basic_captcha_service
        from backend.config import Config
        
        # 创建必要的目录
        Config.create_directories()
        
        print("1. 创建EIR服务...")
        eir_service = EIRService()
        
        print("2. 获取验证码...")
        result = await eir_service.get_login_captcha()
        print(f"验证码获取结果: {result}")
        
        if result["success"]:
            captcha_path = result["captcha_path"]
            print(f"✅ 验证码保存在: {captcha_path}")
            
            print("3. 使用基础识别服务分析验证码...")
            recognition_result = basic_captcha_service.recognize_captcha(captcha_path)
            print(f"识别结果: {recognition_result}")
            
            if recognition_result["success"]:
                best_result = recognition_result["best_result"]
                confidence = recognition_result["confidence"]
                all_results = recognition_result["all_results"]
                features = recognition_result.get("features", {})
                
                print(f"✅ 最佳识别结果: {best_result}")
                print(f"✅ 置信度: {confidence}")
                print(f"✅ 所有候选结果: {all_results}")
                print(f"✅ 图像特征: {features}")
                
                # 显示候选列表
                if len(all_results) > 1:
                    print("\n候选验证码列表:")
                    for i, candidate in enumerate(all_results, 1):
                        print(f"{i}. {candidate}")
                    
                    choice = input(f"\n选择要测试的验证码 (1-{len(all_results)}) 或输入正确答案: ").strip()
                    
                    if choice.isdigit() and 1 <= int(choice) <= len(all_results):
                        test_code = all_results[int(choice) - 1]
                        print(f"选择测试验证码: {test_code}")
                    elif choice:
                        test_code = choice
                        print(f"使用输入的验证码: {test_code}")
                    else:
                        test_code = best_result
                        print(f"使用最佳结果: {test_code}")
                else:
                    test_code = best_result
                    user_input = input(f"是否使用识别结果 '{best_result}' 进行登录测试？(y/n/输入正确验证码): ").strip()
                    if user_input.lower() == 'n':
                        print("跳过登录测试")
                        return
                    elif user_input and user_input.lower() != 'y':
                        test_code = user_input
                        print(f"使用输入的验证码: {test_code}")
                
                # 进行登录测试
                print(f"\n4. 使用验证码 '{test_code}' 进行登录测试...")
                login_result = await eir_service.login(test_code)
                print(f"登录结果: {login_result}")
                
                if login_result["success"]:
                    print("🎉 登录成功！")
                    if test_code == best_result:
                        print("✅ 自动识别准确")
                else:
                    print(f"❌ 登录失败: {login_result['message']}")
                    
                    # 询问正确的验证码
                    correct_code = input("请输入正确的验证码: ").strip()
                    if correct_code:
                        print(f"使用正确验证码 '{correct_code}' 重试...")
                        retry_result = await eir_service.login(correct_code)
                        print(f"重试登录结果: {retry_result}")
                        
                        # 分析识别准确性
                        print(f"\n识别准确性分析:")
                        print(f"系统识别: {best_result}")
                        print(f"正确答案: {correct_code}")
                        print(f"准确性: {'✅ 正确' if correct_code.upper() == best_result.upper() else '❌ 错误'}")
                        
                        # 检查是否在候选列表中
                        if correct_code.upper() in [c.upper() for c in all_results]:
                            print(f"✅ 正确答案在候选列表中")
                        else:
                            print(f"❌ 正确答案不在候选列表中")
                    
            else:
                print(f"❌ 验证码识别失败: {recognition_result['message']}")
        else:
            print(f"❌ 验证码获取失败: {result['message']}")
        
        # 清理资源
        eir_service.close()
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始基础验证码识别测试...")
    
    choice = input("选择测试模式:\n1. 使用现有验证码图片测试\n2. 获取新验证码并测试\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        asyncio.run(test_basic_captcha())
    elif choice == "2":
        asyncio.run(test_with_new_captcha())
    else:
        print("无效选择，默认使用现有验证码测试")
        asyncio.run(test_basic_captcha())

if __name__ == "__main__":
    main()
